# Petric API Postman Collection

This comprehensive Postman collection provides complete API testing capabilities for the Petric Pet Care mobile application backend.

## 📋 Collection Overview

The collection includes **40+ API endpoints** organized into the following categories:

- **🔐 Authentication** - User registration, login, email verification
- **👤 User Management** - Profile management, image uploads, notifications
- **🐾 Pet Management** - CRUD operations for pet profiles
- **🏥 Services** - Service discovery and management
- **📅 Bookings** - Service booking and scheduling
- **💬 Messages** - Real-time messaging system
- **💳 Payments** - Stripe payment processing
- **🔧 Admin** - Administrative functions and analytics
- **🔍 Utility** - Health checks and system status

## 🚀 Quick Start

### 1. Import Collection and Environment

1. **Import Collection**: Import `Petric_API_Collection.postman_collection.json`
2. **Import Environment**: Import `Petric_API_Environment.postman_environment.json`
3. **Select Environment**: Choose "Petric API Environment" in Postman

### 2. Configure Environment Variables

Update the following variables in your environment:

```json
{
  "baseUrl": "http://localhost:3000",
  "testUserEmail": "<EMAIL>",
  "testUserPassword": "your-test-password",
  "stripeTestKey": "pk_test_your_stripe_key"
}
```

### 3. Authentication Flow

1. **Register a new user** or **Login** with existing credentials
2. The JWT token will be **automatically saved** to `{{authToken}}`
3. All authenticated endpoints will use this token automatically

## 🔧 Environment Variables

| Variable | Description | Auto-Set |
|----------|-------------|----------|
| `baseUrl` | API server base URL | ❌ |
| `authToken` | JWT authentication token | ✅ |
| `userId` | Current user ID | ✅ |
| `petId` | Pet ID for testing | ✅ |
| `serviceId` | Service ID for testing | ✅ |
| `bookingId` | Booking ID for testing | ✅ |
| `paymentIntentId` | Stripe payment intent ID | ✅ |

## 📝 API Endpoint Categories

### Authentication Endpoints
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/verify-email` - Verify email address

### User Management
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `POST /api/users/profile/image` - Upload profile image

### Pet Management
- `GET /api/pets` - Get user's pets
- `POST /api/pets` - Add new pet
- `GET /api/pets/:id` - Get pet details
- `PUT /api/pets/:id` - Update pet
- `DELETE /api/pets/:id` - Delete pet

### Services
- `GET /api/services` - Get services with filtering
- `GET /api/services/:id` - Get service details

### Bookings
- `GET /api/bookings` - Get user bookings
- `POST /api/bookings` - Create new booking
- `GET /api/bookings/:id` - Get booking details

### Messages
- `GET /api/messages/chats` - Get chat list
- `GET /api/messages/chat/:userId` - Get chat history
- `POST /api/messages` - Send message
- `GET /api/messages/unread-count` - Get unread count

### Payments
- `POST /api/payments/create-intent` - Create payment intent
- `POST /api/payments/confirm` - Confirm payment
- `GET /api/payments/history` - Get payment history

### Admin (Requires Admin Role)
- `GET /api/admin/dashboard` - Dashboard statistics
- `GET /api/admin/users` - Manage users
- `GET /api/admin/bookings` - Manage bookings
- `GET /api/admin/services` - Manage services

## 🧪 Testing Features

### Automated Tests
Each request includes comprehensive test scripts that validate:
- ✅ Response status codes
- ✅ Response structure and required fields
- ✅ Data types and formats
- ✅ Business logic validation

### Global Test Scripts
- **Response time validation** (< 5000ms)
- **Success field validation**
- **Automatic token extraction** from login responses

### Pre-request Scripts
- **Request logging** for debugging
- **Dynamic variable setting**

## 🔐 Authentication

The collection uses **Bearer Token** authentication:

1. **Automatic Token Management**: Login responses automatically save JWT tokens
2. **Global Authentication**: All protected endpoints inherit the auth token
3. **Token Refresh**: Re-login to refresh expired tokens

## 📊 Sample Data

The collection includes realistic sample data for:
- **User Registration**: Complete user profiles with addresses
- **Pet Profiles**: Various pet types with medical information
- **Service Bookings**: Realistic booking scenarios
- **Payment Processing**: Test payment methods and amounts

## 🚨 Error Handling

The collection tests various error scenarios:
- **400 Bad Request** - Validation errors
- **401 Unauthorized** - Authentication failures
- **403 Forbidden** - Authorization failures
- **404 Not Found** - Resource not found
- **500 Internal Server Error** - Server errors

## 🔄 Workflow Testing

Recommended testing workflow:

1. **Setup**: Health check → Register/Login
2. **Profile Setup**: Update profile → Upload image
3. **Pet Management**: Add pets → Update pet info
4. **Service Discovery**: Browse services → Get service details
5. **Booking Flow**: Create booking → Process payment
6. **Communication**: Send messages → Check chat history
7. **Admin Tasks**: View dashboard → Manage resources

## 📈 Performance Testing

Use Postman's **Collection Runner** for:
- **Load Testing**: Run multiple iterations
- **Performance Monitoring**: Track response times
- **Data-driven Testing**: Use CSV files for test data

## 🛠️ Development Tips

1. **Environment Switching**: Create separate environments for dev/staging/prod
2. **Variable Management**: Use collection variables for shared data
3. **Test Organization**: Group related tests using folders
4. **Documentation**: Each request includes detailed descriptions
5. **Monitoring**: Set up Postman monitors for continuous testing

## 📞 Support

For issues or questions:
- Check the API documentation at `/api-docs` (development mode)
- Review the backend README.md for detailed API specifications
- Ensure all environment variables are properly configured

---

**Happy Testing! 🚀**
