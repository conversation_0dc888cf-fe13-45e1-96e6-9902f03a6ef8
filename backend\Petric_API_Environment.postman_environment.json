{"id": "petric-api-environment", "name": "Petric API Environment", "values": [{"key": "baseUrl", "value": "http://localhost:3000", "description": "Base URL for the Petric API server", "type": "default", "enabled": true}, {"key": "authToken", "value": "", "description": "JWT authentication token (automatically set after login)", "type": "secret", "enabled": true}, {"key": "userId", "value": "", "description": "Current user ID (automatically set after login)", "type": "default", "enabled": true}, {"key": "petId", "value": "", "description": "Pet ID for testing pet-related endpoints", "type": "default", "enabled": true}, {"key": "serviceId", "value": "", "description": "Service ID for testing service-related endpoints", "type": "default", "enabled": true}, {"key": "bookingId", "value": "", "description": "Booking ID for testing booking-related endpoints", "type": "default", "enabled": true}, {"key": "paymentIntentId", "value": "", "description": "Stripe payment intent ID for testing payments", "type": "default", "enabled": true}, {"key": "testUserEmail", "value": "<EMAIL>", "description": "Test user email for development", "type": "default", "enabled": true}, {"key": "testUserPassword", "value": "testPassword123", "description": "Test user password for development", "type": "secret", "enabled": true}, {"key": "testProviderEmail", "value": "<EMAIL>", "description": "Test provider email for development", "type": "default", "enabled": true}, {"key": "testProviderPassword", "value": "providerPassword123", "description": "Test provider password for development", "type": "secret", "enabled": true}, {"key": "testAdminEmail", "value": "<EMAIL>", "description": "Test admin email for development", "type": "default", "enabled": true}, {"key": "testAdminPassword", "value": "adminPassword123", "description": "Test admin password for development", "type": "secret", "enabled": true}, {"key": "stripeTestKey", "value": "pk_test_...", "description": "Stripe publishable test key", "type": "secret", "enabled": true}, {"key": "testLatitude", "value": "40.7128", "description": "Test latitude for location-based searches (NYC)", "type": "default", "enabled": true}, {"key": "testLongitude", "value": "-74.0060", "description": "Test longitude for location-based searches (NYC)", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-12-12T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}