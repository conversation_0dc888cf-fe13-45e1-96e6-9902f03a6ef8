{"version": 3, "file": "stream-decoder.js", "sourceRoot": "", "sources": ["../../src/stream-decoder.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,IAAK,SAIJ;AAJD,WAAK,SAAS;IACZ,+CAAO,CAAA;IACP,yDAAY,CAAA;IACZ,+DAAe,CAAA;AACjB,CAAC,EAJI,SAAS,KAAT,SAAS,QAIb;AAED,MAAa,aAAa;IASxB,YAAoB,oBAA4B;QAA5B,yBAAoB,GAApB,oBAAoB,CAAQ;QARxC,cAAS,GAAc,SAAS,CAAC,OAAO,CAAC;QACzC,qBAAgB,GAAW,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3C,oBAAe,GAAW,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1C,sBAAiB,GAAG,CAAC,CAAC;QACtB,oBAAe,GAAG,CAAC,CAAC;QACpB,uBAAkB,GAAa,EAAE,CAAC;QAClC,yBAAoB,GAAG,CAAC,CAAC;IAEkB,CAAC;IAEpD,KAAK,CAAC,IAAY;QAChB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,MAAc,CAAC;QACnB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,OAAO,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC9B,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;gBACvB,KAAK,SAAS,CAAC,OAAO;oBACpB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;oBAC3D,QAAQ,IAAI,CAAC,CAAC;oBACd,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,YAAY,CAAC;oBACxC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC7B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;oBAC3B,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;oBACzB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;oBAC9B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;oBAC7B,MAAM;gBACR,KAAK,SAAS,CAAC,YAAY;oBACzB,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBAClE,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,eAAe,EACpB,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAC1B,QAAQ,EACR,QAAQ,GAAG,MAAM,CAClB,CAAC;oBACF,IAAI,CAAC,iBAAiB,IAAI,MAAM,CAAC;oBACjC,QAAQ,IAAI,MAAM,CAAC;oBACnB,6BAA6B;oBAC7B,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE,CAAC;wBACjC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAC5D,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;4BACzF,MAAM,IAAI,KAAK,CAAC,qCAAqC,IAAI,CAAC,eAAe,OAAO,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;wBAChH,CAAC;wBACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,eAAe,CAAC;wBACjD,IAAI,IAAI,CAAC,oBAAoB,GAAG,CAAC,EAAE,CAAC;4BAClC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,eAAe,CAAC;wBAC7C,CAAC;6BAAM,CAAC;4BACN,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAC3B,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,EAC7C,CAAC,CACF,CAAC;4BAEF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC;4BACnC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBACvB,CAAC;oBACH,CAAC;oBACD,MAAM;gBACR,KAAK,SAAS,CAAC,eAAe;oBAC5B,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACrE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC;oBACtE,IAAI,CAAC,oBAAoB,IAAI,MAAM,CAAC;oBACpC,QAAQ,IAAI,MAAM,CAAC;oBACnB,gCAAgC;oBAChC,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE,CAAC;wBACpC,6CAA6C;wBAC7C,MAAM,oBAAoB,GAAG;4BAC3B,IAAI,CAAC,gBAAgB;4BACrB,IAAI,CAAC,eAAe;yBACrB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAClC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CACjC,oBAAoB,EACpB,IAAI,CAAC,eAAe,GAAG,CAAC,CACzB,CAAC;wBAEF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC;wBACnC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC7B,CAAC;oBACD,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAtFD,sCAsFC"}