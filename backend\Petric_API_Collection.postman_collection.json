{"info": {"name": "Petric Pet Care API", "description": "Comprehensive API collection for the Petric Pet Care mobile application. This collection includes all endpoints for authentication, user management, pet profiles, service bookings, messaging, payments, and admin functions.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "console.log('Making request to:', pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "// Auto-save auth token from login responses", "if (pm.response.json().data && pm.response.json().data.token) {", "    pm.environment.set('authToken', pm.response.json().data.token);", "    console.log('Auth token saved to environment');", "}"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "apiVersion", "value": "v1", "type": "string"}], "item": [{"name": "🔐 Authentication", "description": "User authentication endpoints including registration, login, email verification, and password reset", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securePassword123\",\n  \"phone\": \"+**********\",\n  \"role\": \"user\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "description": "Register a new user account. Role can be 'user' or 'provider'. Phone is optional."}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Registration successful', function () {", "    pm.response.to.have.status(201);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('token');", "    pm.expect(jsonData.data).to.have.property('user');", "});"]}}]}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securePassword123\",\n  \"fcmToken\": \"optional_firebase_token_for_push_notifications\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "description": "Authenticate user and receive JWT token. FCM token is optional for push notifications."}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Login successful', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('token');", "    pm.expect(jsonData.data).to.have.property('user');", "});"]}}]}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"email_verification_token_from_email\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/verify-email", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify-email"]}, "description": "Verify user email address using token sent via email"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Email verification successful', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"]}}]}]}, {"name": "👤 User Management", "description": "User profile management, image uploads, and notification settings", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/profile", "host": ["{{baseUrl}}"], "path": ["api", "users", "profile"]}, "description": "Get current user's profile information"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Profile retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('user');", "});"]}}]}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"phone\": \"+**********\",\n  \"address\": {\n    \"street\": \"123 Main St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zipCode\": \"10001\",\n    \"country\": \"USA\"\n  },\n  \"notifications\": {\n    \"email\": true,\n    \"push\": true,\n    \"sms\": false\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/users/profile", "host": ["{{baseUrl}}"], "path": ["api", "users", "profile"]}, "description": "Update user profile information"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Profile updated successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"]}}]}, {"name": "Upload Profile Image", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "image", "type": "file", "src": "", "description": "Profile image file (JPEG, PNG, GIF - max 5MB)"}]}, "url": {"raw": "{{baseUrl}}/api/users/profile/image", "host": ["{{baseUrl}}"], "path": ["api", "users", "profile", "image"]}, "description": "Upload user profile image"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Image uploaded successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('imageUrl');", "});"]}}]}]}, {"name": "🐾 Pet Management", "description": "Pet profile management including CRUD operations and image uploads", "item": [{"name": "Get User's Pets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/pets", "host": ["{{baseUrl}}"], "path": ["api", "pets"]}, "description": "Get all pets belonging to the current user"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Pets retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('pets');", "    pm.expect(jsonData.data.pets).to.be.an('array');", "});"]}}]}, {"name": "Add New Pet", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Buddy\",\n  \"type\": \"dog\",\n  \"breed\": \"Golden Retriever\",\n  \"age\": {\n    \"years\": 3,\n    \"months\": 6\n  },\n  \"gender\": \"male\",\n  \"weight\": 30.5,\n  \"color\": \"Golden\",\n  \"description\": \"Friendly and energetic dog\",\n  \"medicalInfo\": {\n    \"vaccinations\": [\"Rabies\", \"DHPP\"],\n    \"allergies\": [\"Chicken\"],\n    \"medications\": [],\n    \"specialNeeds\": \"None\"\n  },\n  \"behaviorNotes\": \"Good with children and other pets\"\n}"}, "url": {"raw": "{{baseUrl}}/api/pets", "host": ["{{baseUrl}}"], "path": ["api", "pets"]}, "description": "Add a new pet to user's profile"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Pet added successfully', function () {", "    pm.response.to.have.status(201);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('pet');", "    // Save pet ID for other requests", "    pm.environment.set('petId', jsonData.data.pet._id);", "});"]}}]}, {"name": "Get Pet Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/pets/{{petId}}", "host": ["{{baseUrl}}"], "path": ["api", "pets", "{{petId}}"]}, "description": "Get detailed information about a specific pet"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Pet details retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('pet');", "});"]}}]}, {"name": "Update Pet", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Buddy Updated\",\n  \"weight\": 32.0,\n  \"description\": \"Updated description\",\n  \"behaviorNotes\": \"Updated behavior notes\"\n}"}, "url": {"raw": "{{baseUrl}}/api/pets/{{petId}}", "host": ["{{baseUrl}}"], "path": ["api", "pets", "{{petId}}"]}, "description": "Update pet information"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Pet updated successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"]}}]}, {"name": "Delete Pet", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/pets/{{petId}}", "host": ["{{baseUrl}}"], "path": ["api", "pets", "{{petId}}"]}, "description": "Soft delete a pet (marks as inactive)"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Pet deleted successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"]}}]}]}, {"name": "🏥 Services", "description": "Pet care service management and discovery", "item": [{"name": "Get Services", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/services?page=1&limit=10&category=veterinary&latitude=40.7128&longitude=-74.0060&radius=10", "host": ["{{baseUrl}}"], "path": ["api", "services"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of services per page (max 50)"}, {"key": "category", "value": "veterinary", "description": "Service category filter"}, {"key": "latitude", "value": "40.7128", "description": "Latitude for location-based search"}, {"key": "longitude", "value": "-74.0060", "description": "Longitude for location-based search"}, {"key": "radius", "value": "10", "description": "Search radius in kilometers"}]}, "description": "Get list of services with optional filtering by category and location"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Services retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('services');", "    pm.expect(jsonData.data).to.have.property('pagination');", "});"]}}]}, {"name": "Get Service Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/services/{{serviceId}}", "host": ["{{baseUrl}}"], "path": ["api", "services", "{{serviceId}}"]}, "description": "Get detailed information about a specific service"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Service details retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('service');", "});"]}}]}]}, {"name": "📅 Bookings", "description": "Service booking management and scheduling", "item": [{"name": "Get User Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/bookings?page=1&limit=10&status=confirmed", "host": ["{{baseUrl}}"], "path": ["api", "bookings"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}, {"key": "status", "value": "confirmed", "description": "Filter by booking status"}]}, "description": "Get user's bookings (as customer or provider)"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Bookings retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('bookings');", "});"]}}]}, {"name": "Create Booking", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"serviceId\": \"{{serviceId}}\",\n  \"petIds\": [\"{{petId}}\"],\n  \"startDate\": \"2024-12-20\",\n  \"endDate\": \"2024-12-20\",\n  \"startTime\": \"09:00\",\n  \"endTime\": \"10:00\",\n  \"specialInstructions\": \"Please be gentle, my pet is nervous around strangers\"\n}"}, "url": {"raw": "{{baseUrl}}/api/bookings", "host": ["{{baseUrl}}"], "path": ["api", "bookings"]}, "description": "Create a new service booking"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Booking created successfully', function () {", "    pm.response.to.have.status(201);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('booking');", "    // Save booking ID for other requests", "    pm.environment.set('bookingId', jsonData.data.booking._id);", "});"]}}]}, {"name": "Get Booking Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/bookings/{{bookingId}}", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "{{bookingId}}"]}, "description": "Get detailed information about a specific booking"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Booking details retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('booking');", "});"]}}]}]}, {"name": "💬 Messages", "description": "Real-time messaging system for communication between users and providers", "item": [{"name": "Get Chat List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/chats", "host": ["{{baseUrl}}"], "path": ["api", "messages", "chats"]}, "description": "Get list of all chats for the current user"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Chat list retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('chats');", "});"]}}]}, {"name": "Get Chat History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/chat/{{userId}}?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["api", "messages", "chat", "{{userId}}"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "50", "description": "Messages per page"}]}, "description": "Get chat history with a specific user"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Chat history retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('messages');", "});"]}}]}, {"name": "Send Message", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipientId\": \"{{userId}}\",\n  \"content\": \"Hello! I'd like to book your pet sitting service.\",\n  \"messageType\": \"text\",\n  \"bookingId\": \"{{bookingId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/messages", "host": ["{{baseUrl}}"], "path": ["api", "messages"]}, "description": "Send a message to another user"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Message sent successfully', function () {", "    pm.response.to.have.status(201);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('message');", "});"]}}]}, {"name": "Get Unread Count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/unread-count", "host": ["{{baseUrl}}"], "path": ["api", "messages", "unread-count"]}, "description": "Get count of unread messages"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Unread count retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('unreadCount');", "});"]}}]}]}, {"name": "💳 Payments", "description": "Stripe payment processing for service bookings", "item": [{"name": "Create Payment Intent", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bookingId\": \"{{bookingId}}\",\n  \"paymentMethodId\": \"pm_card_visa\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/create-intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-intent"]}, "description": "Create a Stripe payment intent for a booking"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Payment intent created successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('clientSecret');", "    pm.expect(jsonData.data).to.have.property('paymentIntentId');", "    // Save payment intent ID", "    pm.environment.set('paymentIntentId', jsonData.data.paymentIntentId);", "});"]}}]}, {"name": "Confirm Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paymentIntentId\": \"{{paymentIntentId}}\",\n  \"paymentMethodId\": \"pm_card_visa\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/confirm", "host": ["{{baseUrl}}"], "path": ["api", "payments", "confirm"]}, "description": "Confirm a payment intent"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Payment confirmed successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('payment');", "});"]}}]}, {"name": "Get Payment History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/history", "host": ["{{baseUrl}}"], "path": ["api", "payments", "history"]}, "description": "Get user's payment history"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Payment history retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('payments');", "});"]}}]}]}, {"name": "🔧 Admin", "description": "Administrative endpoints for managing users, services, and bookings (Admin role required)", "item": [{"name": "Get Dashboard Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/dashboard", "host": ["{{baseUrl}}"], "path": ["api", "admin", "dashboard"]}, "description": "Get admin dashboard statistics and metrics"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Dashboard stats retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('overview');", "    pm.expect(jsonData.data).to.have.property('revenue');", "    pm.expect(jsonData.data).to.have.property('bookingStats');", "});"]}}]}, {"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/users?page=1&limit=20&role=user&search=john", "host": ["{{baseUrl}}"], "path": ["api", "admin", "users"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Users per page (max 100)"}, {"key": "role", "value": "user", "description": "Filter by user role"}, {"key": "search", "value": "john", "description": "Search by name or email"}]}, "description": "Get all users with pagination and filtering"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Users retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('users');", "    pm.expect(jsonData.data).to.have.property('pagination');", "});"]}}]}, {"name": "Get All Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/bookings?page=1&limit=20&status=confirmed", "host": ["{{baseUrl}}"], "path": ["api", "admin", "bookings"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Bookings per page (max 100)"}, {"key": "status", "value": "confirmed", "description": "Filter by booking status"}]}, "description": "Get all bookings with pagination and filtering"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Bookings retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('bookings');", "    pm.expect(jsonData.data).to.have.property('pagination');", "});"]}}]}, {"name": "Get All Services", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/services?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "admin", "services"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Services per page (max 100)"}]}, "description": "Get all services with pagination"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Services retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('services');", "    pm.expect(jsonData.data).to.have.property('pagination');", "});"]}}]}]}, {"name": "🔍 Utility", "description": "Utility endpoints for health checks and system status", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check API health and uptime"}, "response": [], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Health check successful', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('OK');", "    pm.expect(jsonData).to.have.property('timestamp');", "    pm.expect(jsonData).to.have.property('uptime');", "});"]}}]}]}]}