# Changelog

[npm history][1]

[1]: https://www.npmjs.com/package/@google-cloud/storage?activeTab=versions

## [7.16.0](https://github.com/googleapis/nodejs-storage/compare/v7.15.2...v7.16.0) (2025-03-31)


### Features

* Add moveFileAtomic method ([#2586](https://github.com/googleapis/nodejs-storage/issues/2586)) ([e25fb8c](https://github.com/googleapis/nodejs-storage/commit/e25fb8c659dbdf4a08c87fe2a929c8d62e6162e1))

## [7.15.2](https://github.com/googleapis/nodejs-storage/compare/v7.15.1...v7.15.2) (2025-02-20)


### Bug Fixes

* Export SaveData type from index.ts ([#2580](https://github.com/googleapis/nodejs-storage/issues/2580)) ([dbf510c](https://github.com/googleapis/nodejs-storage/commit/dbf510c08f28c720ba66268eea6a62e139179ab4))

## [7.15.1](https://github.com/googleapis/nodejs-storage/compare/v7.15.0...v7.15.1) (2025-02-11)


### Bug Fixes

* **getFiles:** Add nextPageToken to fields for autoPaginate ([#2570](https://github.com/googleapis/nodejs-storage/issues/2570)) ([75c309c](https://github.com/googleapis/nodejs-storage/commit/75c309c0761e4029dcd13024e748d8957052f766))

## [7.15.0](https://github.com/googleapis/nodejs-storage/compare/v7.14.0...v7.15.0) (2024-12-20)


### Features

* Add ability to configure and utilize soft-delete and restore buckets ([#2566](https://github.com/googleapis/nodejs-storage/issues/2566)) ([25cdbb9](https://github.com/googleapis/nodejs-storage/commit/25cdbb918645362ce6994679e8a1c5e7cc666c87))

## [7.14.0](https://github.com/googleapis/nodejs-storage/compare/v7.13.0...v7.14.0) (2024-10-29)


### Features

* Add support for restore token ([#2548](https://github.com/googleapis/nodejs-storage/issues/2548)) ([8241e91](https://github.com/googleapis/nodejs-storage/commit/8241e91e78d47b4cdaea2d941f75fd6a4fa29230))
* Adds integration tests for Universe Domain configuration ([#2538](https://github.com/googleapis/nodejs-storage/issues/2538)) ([53db6ba](https://github.com/googleapis/nodejs-storage/commit/53db6ba7406b99e507cacfa6195cb5a7d308914b))
* Adds integration tests for Universe Domain configuration with ([53db6ba](https://github.com/googleapis/nodejs-storage/commit/53db6ba7406b99e507cacfa6195cb5a7d308914b))
* **storage:** Add support for 'skipIfExists' option for downloadMany ([#2526](https://github.com/googleapis/nodejs-storage/issues/2526)) ([729efb2](https://github.com/googleapis/nodejs-storage/commit/729efb213f96b1a406a1caa54870f50e96796639))

## [7.13.0](https://github.com/googleapis/nodejs-storage/compare/v7.12.1...v7.13.0) (2024-09-17)


### Features

* **storage:** Add support for 'fields' query parameter to getFiles ([#2521](https://github.com/googleapis/nodejs-storage/issues/2521)) ([f78fe92](https://github.com/googleapis/nodejs-storage/commit/f78fe92348a0b383314b4fbfb55638d47af052ff))


### Bug Fixes

* **retry:** Export RETRYABLE_ERR_FN_DEFAULT ([#2517](https://github.com/googleapis/nodejs-storage/issues/2517)) ([db890fd](https://github.com/googleapis/nodejs-storage/commit/db890fd1b25d5cd94e3bcd97ec21fa8f77b1b724))

## [7.12.1](https://github.com/googleapis/nodejs-storage/compare/v7.12.0...v7.12.1) (2024-08-07)


### Bug Fixes

* **deps:** Update fast-xml-parser to 4.4.1 due to security vulnerability ([#2505](https://github.com/googleapis/nodejs-storage/issues/2505)) ([b97d474](https://github.com/googleapis/nodejs-storage/commit/b97d474445efbcde91e690f6ea4160cfc9fd1ed4))

## [7.12.0](https://github.com/googleapis/nodejs-storage/compare/v7.11.3...v7.12.0) (2024-07-15)


### Features

* Add function to allow user to set destination in transfer manager ([#2497](https://github.com/googleapis/nodejs-storage/issues/2497)) ([dc1e488](https://github.com/googleapis/nodejs-storage/commit/dc1e488b50dd7e2deab3e8b28c7d6ece36b90b0e))

## [7.11.3](https://github.com/googleapis/nodejs-storage/compare/v7.11.2...v7.11.3) (2024-07-09)


### Bug Fixes

* Error serialization in resumable-upload.ts ([#2493](https://github.com/googleapis/nodejs-storage/issues/2493)) ([c2e555c](https://github.com/googleapis/nodejs-storage/commit/c2e555c95f7a8e3e231e57e2fa3967caeb860772))
* Handle unhandled error in `startResumableUpload_` ([#2495](https://github.com/googleapis/nodejs-storage/issues/2495)) ([d5257ba](https://github.com/googleapis/nodejs-storage/commit/d5257ba4aa9efc1dd70c303286782d23a95e8568))
* Make CreateBucketRequest extend from BucketMetadata to allow all… ([#2489](https://github.com/googleapis/nodejs-storage/issues/2489)) ([013a5a4](https://github.com/googleapis/nodejs-storage/commit/013a5a45aed8734f797837deb0e805f0ca43a9a6))

## [7.11.2](https://github.com/googleapis/nodejs-storage/compare/v7.11.1...v7.11.2) (2024-06-07)


### Bug Fixes

* Support uint8array in file.save ([#2480](https://github.com/googleapis/nodejs-storage/issues/2480)) ([1477fe1](https://github.com/googleapis/nodejs-storage/commit/1477fe15e5b6dae7cdfb2a1d00121e5d674c8f7c))

## [7.11.1](https://github.com/googleapis/nodejs-storage/compare/v7.11.0...v7.11.1) (2024-05-21)


### Bug Fixes

* Add missing projectIdentifier to GetServiceAccountOptions ([#2468](https://github.com/googleapis/nodejs-storage/issues/2468)) ([d49e9d2](https://github.com/googleapis/nodejs-storage/commit/d49e9d2cbab42eb2390eeeccc562e3283df6384c))
* Allow files in directories to be downloaded onto local machine ([#2199](https://github.com/googleapis/nodejs-storage/issues/2199)) ([9f62429](https://github.com/googleapis/nodejs-storage/commit/9f62429dad234167dc6f0969b40c7942bab83aee))
* Do not set `customEndpoint` if `apiEndpoint === default` ([#2460](https://github.com/googleapis/nodejs-storage/issues/2460)) ([b4dbd73](https://github.com/googleapis/nodejs-storage/commit/b4dbd73189b9fae4c23d614753670ee562bc717b))
* Improve GetFilesResponse interface ([#2466](https://github.com/googleapis/nodejs-storage/issues/2466)) ([918db28](https://github.com/googleapis/nodejs-storage/commit/918db2818395488ff925324a9962879bb56368d7))

## [7.11.0](https://github.com/googleapis/nodejs-storage/compare/v7.10.2...v7.11.0) (2024-05-03)


### Features

* Add ability to enable hierarchical namespace on buckets ([#2453](https://github.com/googleapis/nodejs-storage/issues/2453)) ([4e5726f](https://github.com/googleapis/nodejs-storage/commit/4e5726fe60ecede6bec6508e4b4ec214f2e173dd))

## [7.10.2](https://github.com/googleapis/nodejs-storage/compare/v7.10.1...v7.10.2) (2024-04-26)


### Bug Fixes

* Use correct indices for file.from and fix tests to verify names ([#2449](https://github.com/googleapis/nodejs-storage/issues/2449)) ([d4240fa](https://github.com/googleapis/nodejs-storage/commit/d4240fa5c8c0353de81cc8c052eea2915c3e383c))

## [7.10.1](https://github.com/googleapis/nodejs-storage/compare/v7.10.0...v7.10.1) (2024-04-22)


### Bug Fixes

* Change copyoptions type ([#2439](https://github.com/googleapis/nodejs-storage/issues/2439)) ([2ebd7ac](https://github.com/googleapis/nodejs-storage/commit/2ebd7aca6c474147e5a1d1fb2a96b7d052a08a21))
* Expand types of custom metadata within FileMetadata ([#2442](https://github.com/googleapis/nodejs-storage/issues/2442)) ([1d434a9](https://github.com/googleapis/nodejs-storage/commit/1d434a905392b00bb48ebbb812034e062ed27dd2))

## [7.10.0](https://github.com/googleapis/nodejs-storage/compare/v7.9.0...v7.10.0) (2024-04-15)


### Features

* Add ability to create a File object from URL ([#2432](https://github.com/googleapis/nodejs-storage/issues/2432)) ([1b71fcc](https://github.com/googleapis/nodejs-storage/commit/1b71fcc7687fb8d66e04fb92b15671729f1407e7))
* Allow setting contentEncoding during compose ([#2431](https://github.com/googleapis/nodejs-storage/issues/2431)) ([6e81e05](https://github.com/googleapis/nodejs-storage/commit/6e81e05b2615f1b8307fcca9a147362677e95e7f))


### Bug Fixes

* Destroy pipeline streams when returned stream errors ([#2437](https://github.com/googleapis/nodejs-storage/issues/2437)) ([fe1ac65](https://github.com/googleapis/nodejs-storage/commit/fe1ac655a8d321e225f4828c7adf57342c4a8455))
* Remove extraneous mime-types package in favor of mime ([#2435](https://github.com/googleapis/nodejs-storage/issues/2435)) ([63a71f2](https://github.com/googleapis/nodejs-storage/commit/63a71f2e81181ad976e982ef79d6148913a87c1f))

## [7.9.0](https://github.com/googleapis/nodejs-storage/compare/v7.8.0...v7.9.0) (2024-03-18)


### Features

* Add ability to configure and utilize soft-delete and restore ([#2425](https://github.com/googleapis/nodejs-storage/issues/2425)) ([7da5a7d](https://github.com/googleapis/nodejs-storage/commit/7da5a7da86ad649a8132e3183f4b3e3f9bb2eace))

## [7.8.0](https://github.com/googleapis/nodejs-storage/compare/v7.7.0...v7.8.0) (2024-03-07)


### Features

* Add includeFoldersAsPrefixes for managed folders ([#2413](https://github.com/googleapis/nodejs-storage/issues/2413)) ([3044d3c](https://github.com/googleapis/nodejs-storage/commit/3044d3cfb1b4a24f07fd6ec29e3d20d5818c4ca3))
* Base TPC Support ([#2397](https://github.com/googleapis/nodejs-storage/issues/2397)) ([a3f4891](https://github.com/googleapis/nodejs-storage/commit/a3f4891ee60e57cc19929489cae6110b07955216))


### Bug Fixes

* Cannot read properties of null (reading length) in stream-shift ([#2422](https://github.com/googleapis/nodejs-storage/issues/2422)) ([11ebe2b](https://github.com/googleapis/nodejs-storage/commit/11ebe2bf905f8c15101446ecfe5a2d7c6005d0c3))
* Do not automatically set overrideUnlockedRetention ([#2421](https://github.com/googleapis/nodejs-storage/issues/2421)) ([c781bdc](https://github.com/googleapis/nodejs-storage/commit/c781bdcd89f63b22af1c491a6e517e110331d4ca))

## [7.7.0](https://github.com/googleapis/nodejs-storage/compare/v7.6.0...v7.7.0) (2023-11-29)


### Features

* Implement object retention lock for bucket / files ([#2365](https://github.com/googleapis/nodejs-storage/issues/2365)) ([c140868](https://github.com/googleapis/nodejs-storage/commit/c140868bb5c7d1f0edd586fb2ca55bc613caf5d4))


### Bug Fixes

* TransferManager#downloadFileInChunks issues ([#2373](https://github.com/googleapis/nodejs-storage/issues/2373)) ([65950f3](https://github.com/googleapis/nodejs-storage/commit/65950f3d5c2ed73c56afdf579d7a949b4505e649))

## [7.6.0](https://github.com/googleapis/nodejs-storage/compare/v7.5.0...v7.6.0) (2023-11-08)


### Features

* Resume Resumable Uploads ([#2333](https://github.com/googleapis/nodejs-storage/issues/2333)) ([2ba4009](https://github.com/googleapis/nodejs-storage/commit/2ba4009e599c5690c51849f307199be3452d3b07))

## [7.5.0](https://github.com/googleapis/nodejs-storage/compare/v7.4.0...v7.5.0) (2023-10-30)


### Features

* Support autoclass v2.1 ([#2325](https://github.com/googleapis/nodejs-storage/issues/2325)) ([6572ce9](https://github.com/googleapis/nodejs-storage/commit/6572ce9dc146a0d1e13418a43204058d5909510e))

## [7.4.0](https://github.com/googleapis/nodejs-storage/compare/v7.3.2...v7.4.0) (2023-10-24)


### Features

* Support building in CJS and ESM formats ([#2296](https://github.com/googleapis/nodejs-storage/issues/2296)) ([c848076](https://github.com/googleapis/nodejs-storage/commit/c84807662839b3671230a50b2a0c6f7a6efef528))

## [7.3.2](https://github.com/googleapis/nodejs-storage/compare/v7.3.1...v7.3.2) (2023-10-24)


### Bug Fixes

* Close Open Handle for Empty Objects ([#2338](https://github.com/googleapis/nodejs-storage/issues/2338)) ([c51cd94](https://github.com/googleapis/nodejs-storage/commit/c51cd946171e8749453eef080d2853d31a6e72c8))

## [7.3.1](https://github.com/googleapis/nodejs-storage/compare/v7.3.0...v7.3.1) (2023-10-19)


### Bug Fixes

* Add user-agent header to transfer manager and resumable upload ([#2334](https://github.com/googleapis/nodejs-storage/issues/2334)) ([0520867](https://github.com/googleapis/nodejs-storage/commit/0520867e51a2758ddf2773c0d910c937d55e21b1))
* **deps:** Update dependency retry-request to v7 ([#2327](https://github.com/googleapis/nodejs-storage/issues/2327)) ([f20ef3c](https://github.com/googleapis/nodejs-storage/commit/f20ef3cb7bf8cbdda988e792edc7abd9a1b516f1))
* Destroy sockets to stop memory leaking when stream errors ([#2336](https://github.com/googleapis/nodejs-storage/issues/2336)) ([04939ee](https://github.com/googleapis/nodejs-storage/commit/04939ee4976581cc4168943f0c578bc49458cff7))

## [7.3.0](https://github.com/googleapis/nodejs-storage/compare/v7.2.0...v7.3.0) (2023-10-11)


### Features

* Add transfer manager MPU sample, adjust defaults, prepare for GA ([#2318](https://github.com/googleapis/nodejs-storage/issues/2318)) ([a7d09c1](https://github.com/googleapis/nodejs-storage/commit/a7d09c16aa7f732db089c55d2692ce5fd88c52f3))


### Bug Fixes

* Simplify the code for downloadInChunks ([#2323](https://github.com/googleapis/nodejs-storage/issues/2323)) ([6519929](https://github.com/googleapis/nodejs-storage/commit/6519929153e0006b61d205860de93c9e64b99d81))

## [7.2.0](https://github.com/googleapis/nodejs-storage/compare/v7.1.0...v7.2.0) (2023-10-05)


### Features

* Add headers option to MPU ([#2303](https://github.com/googleapis/nodejs-storage/issues/2303)) ([7f58f30](https://github.com/googleapis/nodejs-storage/commit/7f58f30588735d129fda0503e1daec5f605f8447))
* Transfer Manager Metrics ([#2305](https://github.com/googleapis/nodejs-storage/issues/2305)) ([9be3b6a](https://github.com/googleapis/nodejs-storage/commit/9be3b6a97d9c4685d3c01a6d44e618087be54ea1))


### Bug Fixes

* Make sure destination uses posix separator instead of win ([#2304](https://github.com/googleapis/nodejs-storage/issues/2304)) ([1d4ea74](https://github.com/googleapis/nodejs-storage/commit/1d4ea74a3cc441dfccc47893f0318234f213921b))

## [7.1.0](https://github.com/googleapis/nodejs-storage/compare/v7.0.1...v7.1.0) (2023-09-07)


### Features

* Export `ApiError` ([#2291](https://github.com/googleapis/nodejs-storage/issues/2291)) ([c1d1b35](https://github.com/googleapis/nodejs-storage/commit/c1d1b3505b1c6b0306f632a48cbd7d774b2b94d0))
* Support iterables in file@save ([4356dd0](https://github.com/googleapis/nodejs-storage/commit/4356dd0e6bea5241c4cacd1a58697a332ccf4784))
* Support iterables in file@save ([49327ff](https://github.com/googleapis/nodejs-storage/commit/49327ff576b2367d9efdff2f82a515b0538ee471))
* Support iterables in file@save ([c0d9d58](https://github.com/googleapis/nodejs-storage/commit/c0d9d58b56a9a3485b6c0e5eb92411bb094f7bcb))


### Bug Fixes

* `File#save` iterable fixes ([#2293](https://github.com/googleapis/nodejs-storage/issues/2293)) ([87c3f41](https://github.com/googleapis/nodejs-storage/commit/87c3f419e2a5a3a30ea581aaa6127dfac261be17))
* **deps:** Update dependency @google-cloud/paginator to v5 ([#2263](https://github.com/googleapis/nodejs-storage/issues/2263)) ([0c9b342](https://github.com/googleapis/nodejs-storage/commit/0c9b3425b47c3031ec4bac6d45d8cdca48b2f1a6))
* **deps:** Update dependency @google-cloud/projectify to v4 ([#2264](https://github.com/googleapis/nodejs-storage/issues/2264)) ([c881bae](https://github.com/googleapis/nodejs-storage/commit/c881bae96b40f609d2b7a8d7388c6a76d34faab1))
* **deps:** Update dependency @google-cloud/promisify to v4 ([#2262](https://github.com/googleapis/nodejs-storage/issues/2262)) ([9d46ff3](https://github.com/googleapis/nodejs-storage/commit/9d46ff3c02315c5a3516aa5f2755ee0471ba036b))
* **deps:** Update dependency @google-cloud/pubsub to v4 ([#2256](https://github.com/googleapis/nodejs-storage/issues/2256)) ([18282bb](https://github.com/googleapis/nodejs-storage/commit/18282bbefe1201e51867c676a86301f8086aaf1e))


### Miscellaneous Chores

* Release 7.1.0 ([#2274](https://github.com/googleapis/nodejs-storage/issues/2274)) ([e0f45c2](https://github.com/googleapis/nodejs-storage/commit/e0f45c24b44d11c72ff40cc534be11cb2a65192f))

## [7.0.1](https://github.com/googleapis/nodejs-storage/compare/v7.0.0...v7.0.1) (2023-08-07)


### Bug Fixes

* Export new types from index.ts ([#2258](https://github.com/googleapis/nodejs-storage/issues/2258)) ([93d2a0f](https://github.com/googleapis/nodejs-storage/commit/93d2a0f9f6d6a3de652aaeb3dd914bbc0bb593e0))

## [7.0.0](https://github.com/googleapis/nodejs-storage/compare/v6.12.0...v7.0.0) (2023-08-03)


### ⚠ BREAKING CHANGES

* Make node 14 the minimum supported version ([#2244](https://github.com/googleapis/nodejs-storage/issues/2244))
* Add stronger typings to metadata ([#2234](https://github.com/googleapis/nodejs-storage/issues/2234))
* Remove extend and Treat Provided Options as Mutable ([#2204](https://github.com/googleapis/nodejs-storage/issues/2204))
* Stronger typing for lifecycle rules ([#2215](https://github.com/googleapis/nodejs-storage/issues/2215))
* Do not return responsebody in delete, only raw response ([#2236](https://github.com/googleapis/nodejs-storage/issues/2236))
* Remove extraneous validation in favor of server errors ([#2237](https://github.com/googleapis/nodejs-storage/issues/2237))
* Mark bucket.setLabels and associated interfaces as deprecated ([#2214](https://github.com/googleapis/nodejs-storage/issues/2214))
* Disable source maps for smaller bundle size ([#2240](https://github.com/googleapis/nodejs-storage/issues/2240))



### Features

* Make node 14 the minimum supported version ([#2244](https://github.com/googleapis/nodejs-storage/issues/2244)) ([f48dcd2](https://github.com/googleapis/nodejs-storage/commit/f48dcd2d00081aea8990f35b68a91248f3862abe))

## [6.12.0](https://github.com/googleapis/nodejs-storage/compare/v6.11.0...v6.12.0) (2023-07-13)


### Features

* Add header for deno runtime for metrics tracking ([#2220](https://github.com/googleapis/nodejs-storage/issues/2220)) ([5083920](https://github.com/googleapis/nodejs-storage/commit/50839209063e75996b2a57bd7664760e0e5331ca))
* MPU for transfer manager ([#2192](https://github.com/googleapis/nodejs-storage/issues/2192)) ([ae83421](https://github.com/googleapis/nodejs-storage/commit/ae83421e617a5761c75a7c8a15eaa1ea7c7fb1de))

## [6.11.0](https://github.com/googleapis/nodejs-storage/compare/v6.10.1...v6.11.0) (2023-06-02)


### Features

* Add support for matchGlob list option ([#2206](https://github.com/googleapis/nodejs-storage/issues/2206)) ([79dd839](https://github.com/googleapis/nodejs-storage/commit/79dd8394fbbc0c97aa3acb86ad2248fd58b243b4))

## [6.10.1](https://github.com/googleapis/nodejs-storage/compare/v6.10.0...v6.10.1) (2023-05-10)


### Performance Improvements

* Improve Multiple Chunk Upload Performance ([#2185](https://github.com/googleapis/nodejs-storage/issues/2185)) ([3b2b877](https://github.com/googleapis/nodejs-storage/commit/3b2b87707072e5dc9221a5ba3c727c70db13a593))

## [6.10.0](https://github.com/googleapis/nodejs-storage/compare/v6.9.5...v6.10.0) (2023-05-02)


### Features

* Retry Socket Connection Timeouts on Node 20+ ([#2187](https://github.com/googleapis/nodejs-storage/issues/2187)) ([733b560](https://github.com/googleapis/nodejs-storage/commit/733b560c2f634884dd31c916c208ee6395d4fbe1))

## [6.9.5](https://github.com/googleapis/nodejs-storage/compare/v6.9.4...v6.9.5) (2023-03-30)


### Bug Fixes

* Check that err.message is a string before attempting indexOf ([#2173](https://github.com/googleapis/nodejs-storage/issues/2173)) ([130818d](https://github.com/googleapis/nodejs-storage/commit/130818d291a0004e5d36e5ee72a8c0687b9db181))
* V4 Signing Errors with exactly 7 day expiry ([#2170](https://github.com/googleapis/nodejs-storage/issues/2170)) ([f930998](https://github.com/googleapis/nodejs-storage/commit/f9309985d130a574dd23ecf7b6fb5b58b01d42a0))

## [6.9.4](https://github.com/googleapis/nodejs-storage/compare/v6.9.3...v6.9.4) (2023-03-02)


### Bug Fixes

* Refactor createReadStream to remove unnecessary stream ([#2153](https://github.com/googleapis/nodejs-storage/issues/2153)) ([2c97310](https://github.com/googleapis/nodejs-storage/commit/2c97310da9edd1afbf61711631979433f10249a5))

## [6.9.3](https://github.com/googleapis/nodejs-storage/compare/v6.9.2...v6.9.3) (2023-02-15)


### Bug Fixes

* Reduce memory footprint of deleteFiles by utilizing getFilesStream and using smaller queue of promises ([#2147](https://github.com/googleapis/nodejs-storage/issues/2147)) ([f792f25](https://github.com/googleapis/nodejs-storage/commit/f792f25e3fd38003056f649eaa638a782290cbac))

## [6.9.2](https://github.com/googleapis/nodejs-storage/compare/v6.9.1...v6.9.2) (2023-02-06)


### Bug Fixes

* Correctly handle if a user has no permissions when calling iam.testPermissions ([#2140](https://github.com/googleapis/nodejs-storage/issues/2140)) ([cce902d](https://github.com/googleapis/nodejs-storage/commit/cce902d27cf3c4a23730550b72d10dc76425c974))

## [6.9.1](https://github.com/googleapis/nodejs-storage/compare/v6.9.0...v6.9.1) (2023-01-24)


### Bug Fixes

* Setting file metadata is conditionally idempotent on ifmetagenerationmatch not ifgenerationmatch ([#2131](https://github.com/googleapis/nodejs-storage/issues/2131)) ([f20c28c](https://github.com/googleapis/nodejs-storage/commit/f20c28c5875c9a7095b028912550512459fbf844))

## [6.9.0](https://github.com/googleapis/nodejs-storage/compare/v6.8.0...v6.9.0) (2023-01-04)


### Features

* Add ability to upload entire directory, add samples for upload … ([#2118](https://github.com/googleapis/nodejs-storage/issues/2118)) ([b0f32ce](https://github.com/googleapis/nodejs-storage/commit/b0f32ced04eae1b8ad88a0939fa763cb16b08df9))

## [6.8.0](https://github.com/googleapis/nodejs-storage/compare/v6.7.0...v6.8.0) (2022-12-07)


### Features

* Implement parallel operations ([#2067](https://github.com/googleapis/nodejs-storage/issues/2067)) ([#2109](https://github.com/googleapis/nodejs-storage/issues/2109)) ([ce15b5e](https://github.com/googleapis/nodejs-storage/commit/ce15b5ef68353efbc005cb3a1a780e064ea04deb))

## [6.7.0](https://github.com/googleapis/nodejs-storage/compare/v6.6.0...v6.7.0) (2022-11-03)


### Features

* Support autoclass ([#2078](https://github.com/googleapis/nodejs-storage/issues/2078)) ([7e83580](https://github.com/googleapis/nodejs-storage/commit/7e8358008467dd2d77702734e05f54bc06c9ca5b))

## [6.6.0](https://github.com/googleapis/nodejs-storage/compare/v6.5.4...v6.6.0) (2022-10-25)


### Features

* **crc32c:** Convenient Method For Reading Files ([#2095](https://github.com/googleapis/nodejs-storage/issues/2095)) ([2145c81](https://github.com/googleapis/nodejs-storage/commit/2145c8177f3659fb5f193045866fbcd1220aaeaf))


### Bug Fixes

* Remove async from final function which causes double invocation … ([#2094](https://github.com/googleapis/nodejs-storage/issues/2094)) ([1a3df98](https://github.com/googleapis/nodejs-storage/commit/1a3df985e9096229bc2909921b4974680e12be2a))

## [6.5.4](https://github.com/googleapis/nodejs-storage/compare/v6.5.3...v6.5.4) (2022-10-20)


### Bug Fixes

* Revert STORAGE_EMULATOR_HOST handling ([#2089](https://github.com/googleapis/nodejs-storage/issues/2089)) ([48dce65](https://github.com/googleapis/nodejs-storage/commit/48dce654064470f7496d160d87b696ab5cfd65d4))

## [6.5.3](https://github.com/googleapis/nodejs-storage/compare/v6.5.2...v6.5.3) (2022-10-18)


### Bug Fixes

* Correct STORAGE_EMULATOR_HOST handling ([#2069](https://github.com/googleapis/nodejs-storage/issues/2069), [#1314](https://github.com/googleapis/nodejs-storage/issues/1314)) ([#2070](https://github.com/googleapis/nodejs-storage/issues/2070)) ([c75b8b8](https://github.com/googleapis/nodejs-storage/commit/c75b8b82262dddb794304a71f648bd6e03cafb30))

## [6.5.2](https://github.com/googleapis/nodejs-storage/compare/v6.5.1...v6.5.2) (2022-09-23)


### Bug Fixes

* Determine `Content-Length` Before Attempting Multi-chunk Upload ([#2074](https://github.com/googleapis/nodejs-storage/issues/2074)) ([666402a](https://github.com/googleapis/nodejs-storage/commit/666402a6a65c2ee8e91bc4fe072e91c0b893864e))

## [6.5.1](https://github.com/googleapis/nodejs-storage/compare/v6.5.0...v6.5.1) (2022-09-20)


### Bug Fixes

* Revert skip validation ([#2023](https://github.com/googleapis/nodejs-storage/issues/2023)) ([70ab224](https://github.com/googleapis/nodejs-storage/commit/70ab22459b51b9781e40b0cc86663c1658e43520))

## [6.5.0](https://github.com/googleapis/nodejs-storage/compare/v6.4.2...v6.5.0) (2022-09-15)


### Features

* Add multiple lifecycle rules ([#2062](https://github.com/googleapis/nodejs-storage/issues/2062)) ([fbe2deb](https://github.com/googleapis/nodejs-storage/commit/fbe2deb72bd98db54a03cf228a360a871ba1915b))

## [6.4.2](https://github.com/googleapis/nodejs-storage/compare/v6.4.1...v6.4.2) (2022-09-01)


### Bug Fixes

* remove pip install statements ([#1546](https://github.com/googleapis/nodejs-storage/issues/1546)) ([#2049](https://github.com/googleapis/nodejs-storage/issues/2049)) ([c90ba0f](https://github.com/googleapis/nodejs-storage/commit/c90ba0feecca9d39de3c52f12cc9423b7a2d4d47))
* truncated `createReadStream` through early `end` event ([#2056](https://github.com/googleapis/nodejs-storage/issues/2056)) ([a4716a4](https://github.com/googleapis/nodejs-storage/commit/a4716a4ed1053560f2692647c8a90131763c1e72))

## [6.4.1](https://github.com/googleapis/nodejs-storage/compare/v6.4.0...v6.4.1) (2022-08-12)


### Bug Fixes

* Remove `pumpify` ([#2029](https://github.com/googleapis/nodejs-storage/issues/2029)) ([edc1d64](https://github.com/googleapis/nodejs-storage/commit/edc1d64069a6038c301c3b775f116fbf69b10b28))
* Retry `EPIPE` Connection Errors + Attempt Retries in Resumable Upload Connection Errors ([#2040](https://github.com/googleapis/nodejs-storage/issues/2040)) ([ba35321](https://github.com/googleapis/nodejs-storage/commit/ba35321c3fef9a1874ff8fbea43885e2e7746b4f))

## [6.4.0](https://github.com/googleapis/nodejs-storage/compare/v6.3.0...v6.4.0) (2022-08-10)


### Features

* add functionality for passing preconditions at the function level ([#1993](https://github.com/googleapis/nodejs-storage/issues/1993)) ([21f173e](https://github.com/googleapis/nodejs-storage/commit/21f173eb17d4216e2b42ffdd1ed0104aeda7cf13))

## [6.3.0](https://github.com/googleapis/nodejs-storage/compare/v6.2.3...v6.3.0) (2022-08-01)


### Features

* custom dual regions refactor implementation ([#2012](https://github.com/googleapis/nodejs-storage/issues/2012)) ([9a614fb](https://github.com/googleapis/nodejs-storage/commit/9a614fb2b624f8234fa9d40f352dae177b2b0374))

## [6.2.3](https://github.com/googleapis/nodejs-storage/compare/v6.2.2...v6.2.3) (2022-07-13)


### Bug Fixes

* force setMetadata calls to use promise version, invoke callbacks manually ([#2000](https://github.com/googleapis/nodejs-storage/issues/2000)) ([f488647](https://github.com/googleapis/nodejs-storage/commit/f488647d5ac8ccfdb479d7ea24a377d10573b8c4))

## [6.2.2](https://github.com/googleapis/nodejs-storage/compare/v6.2.1...v6.2.2) (2022-06-29)


### Bug Fixes

* correctly handle an empty file in download function ([#1995](https://github.com/googleapis/nodejs-storage/issues/1995)) ([f1a5a0b](https://github.com/googleapis/nodejs-storage/commit/f1a5a0bd121b84c3d56a9eddbd31593089634574))

## [6.2.1](https://github.com/googleapis/nodejs-storage/compare/v6.2.0...v6.2.1) (2022-06-27)


### Bug Fixes

* explicitly import URL so that .d.ts file is correctly generated ([#1992](https://github.com/googleapis/nodejs-storage/issues/1992)) ([a968f50](https://github.com/googleapis/nodejs-storage/commit/a968f508e7c98e160b658ffc3ab568997548172f))

## [6.2.0](https://github.com/googleapis/nodejs-storage/compare/v6.1.0...v6.2.0) (2022-06-22)


### Features

* Convenient `gs://` URI retrieval ([#1987](https://github.com/googleapis/nodejs-storage/issues/1987)) ([58fad6d](https://github.com/googleapis/nodejs-storage/commit/58fad6d9a3bd92966306e98fd7dedd3992995cb7))


### Bug Fixes

* **deps:** update dependency @google-cloud/projectify to v3 ([#1986](https://github.com/googleapis/nodejs-storage/issues/1986)) ([71a61ec](https://github.com/googleapis/nodejs-storage/commit/71a61ec4ef2436bc091f390af14a02b6920b2b87))
* **deps:** update dependency @google-cloud/pubsub to v3 ([#1972](https://github.com/googleapis/nodejs-storage/issues/1972)) ([004b97e](https://github.com/googleapis/nodejs-storage/commit/004b97ec0ae866997c84ee2327db87e59f510f00))
* Requests Respect `config.projectIdRequired` === `false` ([#1988](https://github.com/googleapis/nodejs-storage/issues/1988)) ([8813369](https://github.com/googleapis/nodejs-storage/commit/881336944be37e15c45b12973064245adc519860))

## [6.1.0](https://github.com/googleapis/nodejs-storage/compare/v6.0.1...v6.1.0) (2022-06-08)


### Features

* support OLM Prefix/Suffix ([#1847](https://github.com/googleapis/nodejs-storage/issues/1847)) ([c22984c](https://github.com/googleapis/nodejs-storage/commit/c22984caa8e8ae09a61d308876b2b3d97503777b))

### [6.0.1](https://github.com/googleapis/nodejs-storage/compare/v6.0.0...v6.0.1) (2022-05-25)


### Bug Fixes

* capture and throw on non-existent files ([#1969](https://github.com/googleapis/nodejs-storage/issues/1969)) ([52d81c0](https://github.com/googleapis/nodejs-storage/commit/52d81c026f30aef0902ea7173dfa6da2e7f97d50))

## [6.0.0](https://github.com/googleapis/nodejs-storage/compare/v5.20.5...v6.0.0) (2022-05-24)


### ⚠ BREAKING CHANGES

* update TypeScript
* remove deprecated fields
* remove configstore
* align resumable upload behavior
* utilize internalized CRC32C utilities
* drop node 10 support

### Build System

* drop node 10 support ([77fa8d9](https://github.com/googleapis/nodejs-storage/commit/77fa8d9f95afbc830b57188ce0d2dfac46476d0b))


### Code Refactoring

* align resumable upload behavior ([77fa8d9](https://github.com/googleapis/nodejs-storage/commit/77fa8d9f95afbc830b57188ce0d2dfac46476d0b))
* remove configstore ([77fa8d9](https://github.com/googleapis/nodejs-storage/commit/77fa8d9f95afbc830b57188ce0d2dfac46476d0b))
* remove deprecated fields ([77fa8d9](https://github.com/googleapis/nodejs-storage/commit/77fa8d9f95afbc830b57188ce0d2dfac46476d0b))
* utilize internalized CRC32C utilities ([77fa8d9](https://github.com/googleapis/nodejs-storage/commit/77fa8d9f95afbc830b57188ce0d2dfac46476d0b))


### deps

* update TypeScript ([77fa8d9](https://github.com/googleapis/nodejs-storage/commit/77fa8d9f95afbc830b57188ce0d2dfac46476d0b))

### [5.20.5](https://github.com/googleapis/nodejs-storage/compare/v5.20.4...v5.20.5) (2022-05-19)


### Bug Fixes

* **chore:** move uuid package to dependencies ([#1952](https://github.com/googleapis/nodejs-storage/issues/1952)) ([0ff5aa3](https://github.com/googleapis/nodejs-storage/commit/0ff5aa3e9ff8b4dcc536b9494e44b9a2fb217727))

### [5.20.4](https://github.com/googleapis/nodejs-storage/compare/v5.20.3...v5.20.4) (2022-05-18)


### Bug Fixes

* revert native typescript mocha tests ([#1947](https://github.com/googleapis/nodejs-storage/issues/1947)) ([1d0ea7d](https://github.com/googleapis/nodejs-storage/commit/1d0ea7d2281a049bc99c6bd810dd24ffc83c6a09))
* support empty object uploads for resumable upload ([#1949](https://github.com/googleapis/nodejs-storage/issues/1949)) ([da6016e](https://github.com/googleapis/nodejs-storage/commit/da6016e20b681d6a75ed1a5459cfd333b58c70a9))

### [5.20.3](https://github.com/googleapis/nodejs-storage/compare/v5.20.2...v5.20.3) (2022-05-17)


### Bug Fixes

* move retrieval of package.json to utility function ([#1941](https://github.com/googleapis/nodejs-storage/issues/1941)) ([ac5cbdf](https://github.com/googleapis/nodejs-storage/commit/ac5cbdf0d3c363e7dab43e9002b01a0dae877642))

### [5.20.2](https://github.com/googleapis/nodejs-storage/compare/v5.20.1...v5.20.2) (2022-05-17)


### Bug Fixes

* use path.join and __dirname for require package.json ([#1936](https://github.com/googleapis/nodejs-storage/issues/1936)) ([b868762](https://github.com/googleapis/nodejs-storage/commit/b86876201ec7d4ce58ae5c1d9635dad82a1fdc4b))

### [5.20.1](https://github.com/googleapis/nodejs-storage/compare/v5.20.0...v5.20.1) (2022-05-16)


### Bug Fixes

* do not use import on package.json ([#1932](https://github.com/googleapis/nodejs-storage/issues/1932)) ([d0f0494](https://github.com/googleapis/nodejs-storage/commit/d0f04941f1cabf7c153f7e5abb91c358f12ef83e))

## [5.20.0](https://github.com/googleapis/nodejs-storage/compare/v5.19.4...v5.20.0) (2022-05-16)


### Features

* add x-goog-api-client headers for retry metrics ([#1920](https://github.com/googleapis/nodejs-storage/issues/1920)) ([0c7e4f6](https://github.com/googleapis/nodejs-storage/commit/0c7e4f6ade4cee1715e14b3cd9abf2aa2a56c0b9))

### [5.19.4](https://github.com/googleapis/nodejs-storage/compare/v5.19.3...v5.19.4) (2022-04-28)


### Bug Fixes

* don't modify passed in options ([#1895](https://github.com/googleapis/nodejs-storage/issues/1895)) ([cd80ca3](https://github.com/googleapis/nodejs-storage/commit/cd80ca318a2b10379b8b166a59f3943b97576475))

### [5.19.3](https://github.com/googleapis/nodejs-storage/compare/v5.19.2...v5.19.3) (2022-04-20)


### Bug Fixes

* export idempotencystrategy and preconditionoptions from index ([#1880](https://github.com/googleapis/nodejs-storage/issues/1880)) ([8aafe04](https://github.com/googleapis/nodejs-storage/commit/8aafe0453a8e2dc41f848dd5165d3e86d6a160ed))

### [5.19.2](https://github.com/googleapis/nodejs-storage/compare/v5.19.1...v5.19.2) (2022-04-14)


### Bug Fixes

* deleting, getting, and getting metadata for notifications ([#1872](https://github.com/googleapis/nodejs-storage/issues/1872)) ([451570e](https://github.com/googleapis/nodejs-storage/commit/451570e6038a1b91b5723db9b941cd916fd76348))

### [5.19.1](https://github.com/googleapis/nodejs-storage/compare/v5.19.0...v5.19.1) (2022-04-08)


### Bug Fixes

* prevent retrying 200 response ([#1857](https://github.com/googleapis/nodejs-storage/issues/1857)) ([638a47b](https://github.com/googleapis/nodejs-storage/commit/638a47b4e7ecc6e94b3b11d1ccc7c52afdeaafe1))

## [5.19.0](https://github.com/googleapis/nodejs-storage/compare/v5.18.3...v5.19.0) (2022-04-06)


### Features

* Dual Region Support ([#1814](https://github.com/googleapis/nodejs-storage/issues/1814)) ([caf7ee5](https://github.com/googleapis/nodejs-storage/commit/caf7ee561fd640b0daea92c7837c47e66070c30c))

### [5.18.3](https://github.com/googleapis/nodejs-storage/compare/v5.18.2...v5.18.3) (2022-03-28)


### Bug Fixes

* encode name portion when calling publicUrl function ([#1828](https://github.com/googleapis/nodejs-storage/issues/1828)) ([5522b35](https://github.com/googleapis/nodejs-storage/commit/5522b35e83857421a00e71c4e93ba6ae0ffccb90))
* fixed typo ([#1803](https://github.com/googleapis/nodejs-storage/issues/1803)) ([be70dae](https://github.com/googleapis/nodejs-storage/commit/be70dae33751ddc3e0ae5a55b5cdbf2002a42932))

### [5.18.2](https://github.com/googleapis/nodejs-storage/compare/v5.18.1...v5.18.2) (2022-02-16)


### Bug Fixes

* resumable uploads should respect autoRetry & multipart uploads should correctly use preconditions ([#1779](https://github.com/googleapis/nodejs-storage/issues/1779)) ([1e72586](https://github.com/googleapis/nodejs-storage/commit/1e725867dce8f78070435b96b65f97f2253c0e80))

### [5.18.1](https://github.com/googleapis/nodejs-storage/compare/v5.18.0...v5.18.1) (2022-01-26)


### Bug Fixes

* **gcs-resumable-upload:** Stop Duplicate Response Handlers on Retries ([#1764](https://github.com/googleapis/nodejs-storage/issues/1764)) ([fe44871](https://github.com/googleapis/nodejs-storage/commit/fe4487187aa405e7d7f8e0bec485bbddb76ea050))

## [5.18.0](https://github.com/googleapis/nodejs-storage/compare/v5.17.0...v5.18.0) (2022-01-18)


### Features

* Expose `chunkSize` param for `CreateResumableUploadOptions` ([#1754](https://github.com/googleapis/nodejs-storage/issues/1754)) ([3acfd5b](https://github.com/googleapis/nodejs-storage/commit/3acfd5b2412d046c471d8d707023e034dc1a167a))

## [5.17.0](https://**************/googleapis/nodejs-storage/compare/v5.16.1...v5.17.0) (2022-01-10)


### Features

* add support for rpo (turbo replication) metadata field when cre… ([#1648](https://**************/googleapis/nodejs-storage/issues/1648)) ([291e6ef](https://**************/googleapis/nodejs-storage/commit/291e6ef48efcfca55b4a7dca8868a57c0eeec89b))


### Bug Fixes

* remove compodoc dev dependency ([#1745](https://**************/googleapis/nodejs-storage/issues/1745)) ([809bf11](https://**************/googleapis/nodejs-storage/commit/809bf11b8a2a2203db82aec38b6a6023a805bd62))

### [5.16.1](https://**************/googleapis/nodejs-storage/compare/v5.16.0...v5.16.1) (2021-11-29)


### Bug Fixes

* change properties with function value to methods ([#1715](https://**************/googleapis/nodejs-storage/issues/1715)) ([c365254](https://**************/googleapis/nodejs-storage/commit/c36525402da8e748971473b1cdd2423e8fd953e1))
* revert skip validation ([#1718](https://**************/googleapis/nodejs-storage/issues/1718)) ([0c75e33](https://**************/googleapis/nodejs-storage/commit/0c75e33eb0291aa7dfc704c86733f4c0dc78d322))
* stop File.download from truncating output file on failure ([#1720](https://**************/googleapis/nodejs-storage/issues/1720)) ([d77979b](https://**************/googleapis/nodejs-storage/commit/d77979b1003dbb89cd9d4725330de50b1f8d9262))

## [5.16.0](https://**************/googleapis/nodejs-storage/compare/v5.15.5...v5.16.0) (2021-11-09)


### Features

* improved error messages for resumable uploads ([#1708](https://**************/googleapis/nodejs-storage/issues/1708)) ([50cdbb6](https://**************/googleapis/nodejs-storage/commit/50cdbb6f730ee7f96cb598c4cda412fc4bcc8807))


### Bug Fixes

* add scenario 3 conformance tests ([#1702](https://**************/googleapis/nodejs-storage/issues/1702)) ([e16a3a5](https://**************/googleapis/nodejs-storage/commit/e16a3a5eb09a388743259d54c31e62d7fc220bf0))
* retry uri creation dep update & conformance tests ([#1700](https://**************/googleapis/nodejs-storage/issues/1700)) ([d265f8c](https://**************/googleapis/nodejs-storage/commit/d265f8c5e4e6a8c8239e959dfb4d0acbf4cdfe0a))

### [5.15.6](https://**************/googleapis/nodejs-storage/compare/v5.15.5...v5.15.6) (2021-11-05)


### Bug Fixes

* add scenario 3 conformance tests ([#1702](https://**************/googleapis/nodejs-storage/issues/1702)) ([e16a3a5](https://**************/googleapis/nodejs-storage/commit/e16a3a5eb09a388743259d54c31e62d7fc220bf0))
* retry uri creation dep update & conformance tests ([#1700](https://**************/googleapis/nodejs-storage/issues/1700)) ([d265f8c](https://**************/googleapis/nodejs-storage/commit/d265f8c5e4e6a8c8239e959dfb4d0acbf4cdfe0a))

### [5.15.5](https://**************/googleapis/nodejs-storage/compare/v5.15.4...v5.15.5) (2021-11-03)


### Bug Fixes

* **deps:** update dependency mime to v3 ([#1696](https://**************/googleapis/nodejs-storage/issues/1696)) ([f337208](https://**************/googleapis/nodejs-storage/commit/f33720883bb6d797d2fb89d5e6ff9584d216be74))
* explicitly define function type of getFilesStream ([#1697](https://**************/googleapis/nodejs-storage/issues/1697)) ([c950c23](https://**************/googleapis/nodejs-storage/commit/c950c23742bb9291a3e15b95ae0ee4a13466c361))

### [5.15.4](https://**************/googleapis/nodejs-storage/compare/v5.15.3...v5.15.4) (2021-11-01)


### Bug Fixes

* check e is not null ([#1692](https://**************/googleapis/nodejs-storage/issues/1692)) ([56ff485](https://**************/googleapis/nodejs-storage/commit/56ff485cbe28ba048c9a689711b034c416853b1f))

### [5.15.3](https://**************/googleapis/nodejs-storage/compare/v5.15.2...v5.15.3) (2021-10-14)


### Bug Fixes

* do not use src precondition options in copy. ([#1666](https://**************/googleapis/nodejs-storage/issues/1666)) ([678ae77](https://**************/googleapis/nodejs-storage/commit/678ae77dfb2c1eb2272734f34315b4d0ec726076))

### [5.15.2](https://**************/googleapis/nodejs-storage/compare/v5.15.1...v5.15.2) (2021-10-13)


### Bug Fixes

* remove bucket preconditions from deleteFiles, it is a file operation not bucket ([#1661](https://**************/googleapis/nodejs-storage/issues/1661)) ([6b7a06d](https://**************/googleapis/nodejs-storage/commit/6b7a06defe1a3cadc6fad9258ff3fb01a2ecce0a))

### [5.15.1](https://**************/googleapis/nodejs-storage/compare/v5.15.0...v5.15.1) (2021-10-12)


### Bug Fixes

* check generation on source files not metageneration on bucket ([#1654](https://**************/googleapis/nodejs-storage/issues/1654)) ([760231c](https://**************/googleapis/nodejs-storage/commit/760231ca520f4eedf878c245489cb07f95e153af))

## [5.15.0](https://**************/googleapis/nodejs-storage/compare/v5.14.7...v5.15.0) (2021-10-07)


### Features

* add support for useAuthWithCustomEndpoint option ([#1645](https://**************/googleapis/nodejs-storage/issues/1645)) ([d11d6b4](https://**************/googleapis/nodejs-storage/commit/d11d6b4b2678cb240928e2dfe20f983d2ae896f6))


### Bug Fixes

* update common dep ([#1644](https://**************/googleapis/nodejs-storage/issues/1644)) ([793467f](https://**************/googleapis/nodejs-storage/commit/793467f25abf46eb7ba5e6cd1b80f735faa035dc))

### [5.14.8](https://**************/googleapis/nodejs-storage/compare/v5.14.7...v5.14.8) (2021-10-06)


### Bug Fixes

* update common dep ([#1644](https://**************/googleapis/nodejs-storage/issues/1644)) ([793467f](https://**************/googleapis/nodejs-storage/commit/793467f25abf46eb7ba5e6cd1b80f735faa035dc))

### [5.14.7](https://**************/googleapis/nodejs-storage/compare/v5.14.6...v5.14.7) (2021-10-06)


### Bug Fixes

* file preconditions should respect ifGenerationMatch not ifMetagenerationMatch ([#1642](https://**************/googleapis/nodejs-storage/issues/1642)) ([eb6f31f](https://**************/googleapis/nodejs-storage/commit/eb6f31f3f6c439e21bbc0cd46f32a7327e15f65e))

### [5.14.6](https://**************/googleapis/nodejs-storage/compare/v5.14.5...v5.14.6) (2021-10-06)


### Bug Fixes

* pass precondition opts to new file ([#1638](https://**************/googleapis/nodejs-storage/issues/1638)) ([1523ca9](https://**************/googleapis/nodejs-storage/commit/1523ca962fda070cf60e5b81d062e3a291461c83))

### [5.14.5](https://**************/googleapis/nodejs-storage/compare/v5.14.4...v5.14.5) (2021-10-01)


### Bug Fixes

* fix logic for buckets that do not have a generation ([#1634](https://**************/googleapis/nodejs-storage/issues/1634)) ([9069bdc](https://**************/googleapis/nodejs-storage/commit/9069bdc9a0ab495ab62033f432ea0e180e3b182e))
* updated connection reset string ([#1632](https://**************/googleapis/nodejs-storage/issues/1632)) ([b841d5b](https://**************/googleapis/nodejs-storage/commit/b841d5b98c3d98b5f1dc7776a887159294eb0b36))

### [5.14.4](https://**************/googleapis/nodejs-storage/compare/v5.14.3...v5.14.4) (2021-09-27)


### Bug Fixes

* respect precondition settings from constructors ([#1617](https://**************/googleapis/nodejs-storage/issues/1617)) ([6a48942](https://**************/googleapis/nodejs-storage/commit/6a48942e540e3d96e2a5b396b8e74cbe732178be))

### [5.14.3](https://**************/googleapis/nodejs-storage/compare/v5.14.2...v5.14.3) (2021-09-22)


### Bug Fixes

* set autoretry back to instance value at end of file functions ([#1604](https://**************/googleapis/nodejs-storage/issues/1604)) ([db3b59d](https://**************/googleapis/nodejs-storage/commit/db3b59d731c9760d55bf112c211bdd644da911c4))

### [5.14.2](https://**************/googleapis/nodejs-storage/compare/v5.14.1...v5.14.2) (2021-09-13)


### Bug Fixes

* **build:** set default branch to main ([#1587](https://**************/googleapis/nodejs-storage/issues/1587)) ([b39ce95](https://**************/googleapis/nodejs-storage/commit/b39ce95a2ec9d8dd2114863898181ea10670d962))

### [5.14.1](https://**************/googleapis/nodejs-storage/compare/v5.14.0...v5.14.1) (2021-09-08)


### Bug Fixes

* **types:** remove duplicated definition of BucketOptions and make sure proper version is exported ([#1583](https://**************/googleapis/nodejs-storage/issues/1583)) ([d8f4bc5](https://**************/googleapis/nodejs-storage/commit/d8f4bc59bd3e2cebfe6494842414cd9f7e32018f))

## [5.14.0](https://**************/googleapis/nodejs-storage/compare/v5.13.2...v5.14.0) (2021-08-26)


### Features

* retries for conditionally idempotent operations ([#1561](https://**************/googleapis/nodejs-storage/issues/1561)) ([653f4b4](https://**************/googleapis/nodejs-storage/commit/653f4b488e8603e4008e51b45920fb7de7138eab))


### Bug Fixes

* allow retries of metadatata operations in system tests ([#1568](https://**************/googleapis/nodejs-storage/issues/1568)) ([9398566](https://**************/googleapis/nodejs-storage/commit/939856680d279dcd9587328d0cc58f789b022f5a))

### [5.13.2](https://**************/googleapis/nodejs-storage/compare/v5.13.1...v5.13.2) (2021-08-26)


### Bug Fixes

* update getLabels definition to actually allow no arguments when used in typescript ([#1559](https://**************/googleapis/nodejs-storage/issues/1559)) ([176dbb5](https://**************/googleapis/nodejs-storage/commit/176dbb5223f4442d10fd098ffa2cda5cf12144f2))

### [5.13.1](https://**************/googleapis/nodejs-storage/compare/v5.13.0...v5.13.1) (2021-08-18)


### Bug Fixes

* **deps:** update dependency date-and-time to v2 ([#1537](https://**************/googleapis/nodejs-storage/issues/1537)) ([9d0d69e](https://**************/googleapis/nodejs-storage/commit/9d0d69eaf908817dec274abe915bd96bb22c663a))

## [5.13.0](https://**************/googleapis/nodejs-storage/compare/v5.12.0...v5.13.0) (2021-08-09)


### Features

* Precondition checks ([#1523](https://**************/googleapis/nodejs-storage/issues/1523)) ([7c24417](https://**************/googleapis/nodejs-storage/commit/7c244178649f120cfefe58994b515da7ca6b7ffb))

## [5.12.0](https://**************/googleapis/nodejs-storage/compare/v5.11.1...v5.12.0) (2021-08-03)


### Features

* pass precondition parameters to gcs-resumable-upload ([#1516](https://**************/googleapis/nodejs-storage/issues/1516)) ([65211dd](https://**************/googleapis/nodejs-storage/commit/65211ddb8ae19229154b4aca3d5ff97f2aaa9f56))

### [5.11.1](https://**************/googleapis/nodejs-storage/compare/v5.11.0...v5.11.1) (2021-08-02)


### Bug Fixes

* don't retry non-idempotent functions ([#1517](https://**************/googleapis/nodejs-storage/issues/1517)) ([c938795](https://**************/googleapis/nodejs-storage/commit/c938795e8a5f14ba7724f2d0e334310dd8e8f207))

## [5.11.0](https://**************/googleapis/nodejs-storage/compare/v5.10.0...v5.11.0) (2021-07-23)


### Features

* retries for resumable bucket.upload and file.save ([#1511](https://**************/googleapis/nodejs-storage/issues/1511)) ([9bf163c](https://**************/googleapis/nodejs-storage/commit/9bf163c3e3569bad1440940ff1a6bfd42404bb32))

## [5.10.0](https://**************/googleapis/nodejs-storage/compare/v5.9.0...v5.10.0) (2021-07-22)


### Features

* retry multipart Bucket.upload ([#1509](https://**************/googleapis/nodejs-storage/issues/1509)) ([730d0a0](https://**************/googleapis/nodejs-storage/commit/730d0a0d4a6aa5192d998c54292d3423d3ddeaaa))

## [5.9.0](https://**************/googleapis/nodejs-storage/compare/v5.8.5...v5.9.0) (2021-07-21)


### Features

* customize retry behavior implementation ([#1474](https://**************/googleapis/nodejs-storage/issues/1474)) ([#1493](https://**************/googleapis/nodejs-storage/issues/1493)) ([49008e3](https://**************/googleapis/nodejs-storage/commit/49008e313b89ce6035543bf2cf1e60e253404520))

### [5.8.5](https://**************/googleapis/nodejs-storage/compare/v5.8.4...v5.8.5) (2021-05-04)


### Bug Fixes

* **deps:** updated gcs-resumable-upload dependency ([#1459](https://**************/googleapis/nodejs-storage/issues/1459)) ([afaccc7](https://**************/googleapis/nodejs-storage/commit/afaccc70375a2c778e4306a59bf8a86736c17f6c))

### [5.8.4](https://**************/googleapis/nodejs-storage/compare/v5.8.3...v5.8.4) (2021-04-19)


### Bug Fixes

* **deps:** update dependency date-and-time to v1 ([#1434](https://**************/googleapis/nodejs-storage/issues/1434)) ([91ee6ca](https://**************/googleapis/nodejs-storage/commit/91ee6cab38769d36b00d702e17b222518ad4e752))
* don't fail if ~/.config doesn't exist ([#1428](https://**************/googleapis/nodejs-storage/issues/1428)) ([3cfaba1](https://**************/googleapis/nodejs-storage/commit/3cfaba19d4223c68f4382c06674f135838d32eb8))

### [5.8.3](https://**************/googleapis/nodejs-storage/compare/v5.8.2...v5.8.3) (2021-03-29)


### Bug Fixes

* update CopyOptions type to include cacheControl, contentType and contentEncoding ([#1426](https://**************/googleapis/nodejs-storage/issues/1426)) ([efa5bb8](https://**************/googleapis/nodejs-storage/commit/efa5bb8a22ab68c0c3e8549850e5db4d57ff29bb))

### [5.8.2](https://**************/googleapis/nodejs-storage/compare/v5.8.1...v5.8.2) (2021-03-23)


### Bug Fixes

* **perf:** pull hashes without refreshing metadata ([#1419](https://**************/googleapis/nodejs-storage/issues/1419)) ([f3ec627](https://**************/googleapis/nodejs-storage/commit/f3ec6278df3c3df4d4cddf5293be4dda95f0cbf7))

### [5.8.1](https://**************/googleapis/nodejs-storage/compare/v5.8.0...v5.8.1) (2021-03-02)


### Bug Fixes

* deprecate `options.promise` and sync options with Service ([#1391](https://**************/googleapis/nodejs-storage/issues/1391)) ([59cfe27](https://**************/googleapis/nodejs-storage/commit/59cfe272de16c41e9c768953a25677294f520cc7))
* **types:** support metadata override in file.copy() ([#1406](https://**************/googleapis/nodejs-storage/issues/1406)) ([dda6d30](https://**************/googleapis/nodejs-storage/commit/dda6d305638a07cbca188e459544393d8624f4f0))

## [5.8.0](https://**************/googleapis/nodejs-storage/compare/v5.7.4...v5.8.0) (2021-02-18)


### Features

* adds support workload identity federation ([#1404](https://**************/googleapis/nodejs-storage/issues/1404)) ([7d3a3f1](https://**************/googleapis/nodejs-storage/commit/7d3a3f148361e56cbcd4dc4c8fb178b75f9208bf))

### [5.7.4](https://**************/googleapis/nodejs-storage/compare/v5.7.3...v5.7.4) (2021-02-01)


### Bug Fixes

* specified acceptable types for File.save data parameter ([#1388](https://**************/googleapis/nodejs-storage/issues/1388)) ([607f6c1](https://**************/googleapis/nodejs-storage/commit/607f6c1c8b9ae5414513957f54a5de2490c454b1))

### [5.7.3](https://**************/googleapis/nodejs-storage/compare/v5.7.2...v5.7.3) (2021-01-22)


### Bug Fixes

* retry multipart uploads on File.save() ([#1385](https://**************/googleapis/nodejs-storage/issues/1385)) ([4dec8c1](https://**************/googleapis/nodejs-storage/commit/4dec8c1a362e3f80cbbf49f8bf7e7eaa2e2ce3bc))

### [5.7.2](https://**************/googleapis/nodejs-storage/compare/v5.7.1...v5.7.2) (2021-01-11)


### Bug Fixes

* deprecated directory. prefix should be used instead ([#1370](https://**************/googleapis/nodejs-storage/issues/1370)) ([fae4c06](https://**************/googleapis/nodejs-storage/commit/fae4c0635909a831d65bd3111b27250795d4735b))

### [5.7.1](https://**************/googleapis/nodejs-storage/compare/v5.7.0...v5.7.1) (2021-01-07)


### Bug Fixes

* bump dependencies See [#1372](https://**************/googleapis/nodejs-storage/issues/1372) for details ([#1375](https://**************/googleapis/nodejs-storage/issues/1375)) ([7cf0264](https://**************/googleapis/nodejs-storage/commit/7cf0264c0cea2e0d668818924aaa737381c07bfa))

## [5.7.0](https://**************/googleapis/nodejs-storage/compare/v5.6.0...v5.7.0) (2020-12-10)


### Features

* support metadata updates from makePrivate() methods ([#1355](https://**************/googleapis/nodejs-storage/issues/1355)) ([3db1e83](https://**************/googleapis/nodejs-storage/commit/3db1e832af1cd3a394305b0a1120953d85f86249))


### Bug Fixes

* capitalize action in Bucket#addLifecycleRule ([#1358](https://**************/googleapis/nodejs-storage/issues/1358)) ([205f39f](https://**************/googleapis/nodejs-storage/commit/205f39f970639c06258fb1e0cd18a4d963729262))
* jsdoc for bucket ([#1359](https://**************/googleapis/nodejs-storage/issues/1359)) ([5fa530a](https://**************/googleapis/nodejs-storage/commit/5fa530ab909e837558deecd7e1aa50a7cc4f5830))

## [5.6.0](https://**************/googleapis/nodejs-storage/compare/v5.5.1...v5.6.0) (2020-12-02)


### Features

* allow ignoring 404 errors during delete() operations ([#1347](https://**************/googleapis/nodejs-storage/issues/1347)) ([dab0e7d](https://**************/googleapis/nodejs-storage/commit/dab0e7d9345499411cec57186ef7404026f041eb))

### [5.5.1](https://**************/googleapis/nodejs-storage/compare/v5.5.0...v5.5.1) (2020-12-01)


### Bug Fixes

* add publicUrl to exclude promisifyAll ([#1339](https://**************/googleapis/nodejs-storage/issues/1339)) ([ea2c2c9](https://**************/googleapis/nodejs-storage/commit/ea2c2c9d670b8a8eefffa3e67dd2599135c0d933))
* error if both `storageClass` and specific storage class name are provided ([#1323](https://**************/googleapis/nodejs-storage/issues/1323)) ([91a65f8](https://**************/googleapis/nodejs-storage/commit/91a65f86fe5f9608437b91d6e67192c78b0e8d7b))
* only throw if `storageClass` and specific storage class name provide different values ([#1346](https://**************/googleapis/nodejs-storage/issues/1346)) ([1765608](https://**************/googleapis/nodejs-storage/commit/1765608430d98c555e4a7431c28cd5878e65c7df))
* **docs:** explain manual pagination and add usage sample ([#1317](https://**************/googleapis/nodejs-storage/issues/1317)) ([16b779d](https://**************/googleapis/nodejs-storage/commit/16b779de912f6ac082ec5ee3d904b125a5526485))

## [5.5.0](https://**************/googleapis/nodejs-storage/compare/v5.4.0...v5.5.0) (2020-11-03)


### Features

* accessibleAt v4 signed urls ([#1328](https://**************/googleapis/nodejs-storage/issues/1328)) ([1e0295e](https://**************/googleapis/nodejs-storage/commit/1e0295eebd1120ce6cbcabee4cf1aaa825455d4b))

## [5.4.0](https://**************/googleapis/nodejs-storage/compare/v5.3.0...v5.4.0) (2020-10-29)


### Features

* **userAgent:** allow for optional user agent to be provided ([#1313](https://**************/googleapis/nodejs-storage/issues/1313)) ([13a064f](https://**************/googleapis/nodejs-storage/commit/13a064f3e7342640c5c6e64e060e8558062a31c7))
* add custom time field in metadata sample ([#1285](https://**************/googleapis/nodejs-storage/issues/1285)) ([9e3474f](https://**************/googleapis/nodejs-storage/commit/9e3474f87dabd300d914c0f44f49a13dae34227d))
* add File#rename ([#1311](https://**************/googleapis/nodejs-storage/issues/1311)) ([c77eaa3](https://**************/googleapis/nodejs-storage/commit/c77eaa363be18b6b9ddd1f7b505e23be8cd5bf98))
* public url of a file ([#1324](https://**************/googleapis/nodejs-storage/issues/1324)) ([5ec256e](https://**************/googleapis/nodejs-storage/commit/5ec256e58d04dbcb8dee8cde7460b306da5ff880))


### Bug Fixes

* set customEndpoint for custom environments ([#1316](https://**************/googleapis/nodejs-storage/issues/1316)) ([60910e1](https://**************/googleapis/nodejs-storage/commit/60910e1903f95a0abca6b9855d11833b32666e2c))
* **deps:** update dependency gaxios to v4 ([#1322](https://**************/googleapis/nodejs-storage/issues/1322)) ([f9b16d8](https://**************/googleapis/nodejs-storage/commit/f9b16d82e5c6a26d76f721a277cf07b518b63a42))
* moved publicUrl comment ([#1327](https://**************/googleapis/nodejs-storage/issues/1327)) ([249ed2c](https://**************/googleapis/nodejs-storage/commit/249ed2c4e5fe9cfb029ca8e60c1bafab5c370a48))
* self-upload files for Unicode system tests ([#1318](https://**************/googleapis/nodejs-storage/issues/1318)) ([e826a52](https://**************/googleapis/nodejs-storage/commit/e826a526c3a5a8515cc7a07c15f86fcfb0fb2f93))
* **deps:** update dependency duplexify to v4 ([#1302](https://**************/googleapis/nodejs-storage/issues/1302)) ([1ce3d89](https://**************/googleapis/nodejs-storage/commit/1ce3d895b3f5d12776f0634b41b1baef7aa4491c))
* **deps:** update dependency yargs to v16 ([#1289](https://**************/googleapis/nodejs-storage/issues/1289)) ([12f2133](https://**************/googleapis/nodejs-storage/commit/12f2133b7e3ec9612b3694d93b9ff756cb1bbd66))
* **types:** add `CreateBucketRequest.storageClass` ([#1299](https://**************/googleapis/nodejs-storage/issues/1299)) ([d854c26](https://**************/googleapis/nodejs-storage/commit/d854c2609eb87886a010e726daa5f1339ee9ea38)), closes [#1248](https://**************/googleapis/nodejs-storage/issues/1248)

## [5.3.0](https://**************/googleapis/nodejs-storage/compare/v5.2.0...v5.3.0) (2020-08-24)


### Features

* support noncurrent time object lifecycle condition ([#1216](https://**************/googleapis/nodejs-storage/issues/1216)) ([d198aff](https://**************/googleapis/nodejs-storage/commit/d198affa98b0dd027d6628eaff9fcd2dca5c7b47))

## [5.2.0](https://**************/googleapis/nodejs-storage/compare/v5.1.2...v5.2.0) (2020-08-10)


### Features

* remove through2 dependency ([#1240](https://**************/googleapis/nodejs-storage/issues/1240)) ([97c73dd](https://**************/googleapis/nodejs-storage/commit/97c73ddec4dbb7cbf7a1486c16ed24dbe9be6d83))


### Bug Fixes

* support request interceptors for file uploads ([#1225](https://**************/googleapis/nodejs-storage/issues/1225)) ([aa4e4ec](https://**************/googleapis/nodejs-storage/commit/aa4e4ecf49c2e67fdeb90d54e06335fe8671c185))
* **deps:** update dependency date-and-time to ^0.14.0 ([#1263](https://**************/googleapis/nodejs-storage/issues/1263)) ([408aff9](https://**************/googleapis/nodejs-storage/commit/408aff9234b30f4d68bc9027fbc3088ab059578a))

### [5.1.2](https://**************/googleapis/nodejs-storage/compare/v5.1.1...v5.1.2) (2020-07-06)


### Bug Fixes

* **deps:** update dependency through2 to v4 ([#1226](https://**************/googleapis/nodejs-storage/issues/1226)) ([2be25e8](https://**************/googleapis/nodejs-storage/commit/2be25e8fbd4bc60f8e702c2120cf99510508cd15))

### [5.1.1](https://**************/googleapis/nodejs-storage/compare/v5.1.0...v5.1.1) (2020-06-19)


### Bug Fixes

* **deps:** update dependency p-limit to v3 ([#1210](https://**************/googleapis/nodejs-storage/issues/1210)) ([7da0379](https://**************/googleapis/nodejs-storage/commit/7da03797791ed20ffbbc6456c7cfad1653c59e76))
* update node issue template ([#1220](https://**************/googleapis/nodejs-storage/issues/1220)) ([eaac92b](https://**************/googleapis/nodejs-storage/commit/eaac92bb789aea32718280fb67c6496022fa56d3))

## [5.1.0](https://**************/googleapis/nodejs-storage/compare/v5.0.1...v5.1.0) (2020-06-11)


### Features

* **secrets:** begin migration to secret manager from keystore ([#1211](https://**************/googleapis/nodejs-storage/issues/1211)) ([11b16ae](https://**************/googleapis/nodejs-storage/commit/11b16ae1b06273ab169d7b1f65a54196956ef4ea))
* allow setting timeouts on uploads ([#1208](https://**************/googleapis/nodejs-storage/issues/1208)) ([01b3691](https://**************/googleapis/nodejs-storage/commit/01b3691bbfc4a93e2229ea07184637479c515183))


### Bug Fixes

* **deps:** update dependency @google-cloud/pubsub to v2 ([#1201](https://**************/googleapis/nodejs-storage/issues/1201)) ([c684b2a](https://**************/googleapis/nodejs-storage/commit/c684b2abe99169a8801d76fda38a4cbfba91f417))

### [5.0.1](https://**************/googleapis/nodejs-storage/compare/v5.0.0...v5.0.1) (2020-05-20)


### Bug Fixes

* StorageOptions.apiEndpoint overrides simple and resumable uploads ([#1161](https://**************/googleapis/nodejs-storage/issues/1161)) ([cf8ea5c](https://**************/googleapis/nodejs-storage/commit/cf8ea5cbbecea50233b1681c7f8aba121ec37a1a))
* **types:** assert in typescript 3.7 ([#1198](https://**************/googleapis/nodejs-storage/issues/1198)) ([d5aa8b7](https://**************/googleapis/nodejs-storage/commit/d5aa8b7892366c9fbbdc33dd001f644da61bb22e))

## [5.0.0](https://**************/googleapis/nodejs-storage/compare/v4.7.0...v5.0.0) (2020-05-13)


### ⚠ BREAKING CHANGES

* automatically detect contentType if not provided (#1190)
* drop keepAcl parameter in file copy (#1166)
* drop support for node.js 8.x

### Features

* automatically detect contentType if not provided ([#1190](https://**************/googleapis/nodejs-storage/issues/1190)) ([b31ba4a](https://**************/googleapis/nodejs-storage/commit/b31ba4a11399b57538ddf0d6ca2e10b2aa3fbc3a))
* enable bytes read tracking ([#1074](https://**************/googleapis/nodejs-storage/issues/1074)) ([0776a04](https://**************/googleapis/nodejs-storage/commit/0776a044f3b2149b485e114369e952688df75645))


### Bug Fixes

* **bucket:** Only disable resumable uploads for bucket.upload (fixes [#1133](https://**************/googleapis/nodejs-storage/issues/1133)) ([#1135](https://**************/googleapis/nodejs-storage/issues/1135)) ([2c20148](https://**************/googleapis/nodejs-storage/commit/2c201486b7b2d3146846ac96c877a904c4a674b0)), closes [/github.com/googleapis/nodejs-storage/pull/1135#issuecomment-620070038](https://**************/googleapis//github.com/googleapis/nodejs-storage/pull/1135/issues/issuecomment-620070038)
* add whitespace to generateV4SignedPolicy ([#1136](https://**************/googleapis/nodejs-storage/issues/1136)) ([dcee78b](https://**************/googleapis/nodejs-storage/commit/dcee78b98da23b02fe7d2f13a9270546bc07bba8))
* apache license URL ([#468](https://**************/googleapis/nodejs-storage/issues/468)) ([#1151](https://**************/googleapis/nodejs-storage/issues/1151)) ([e8116d3](https://**************/googleapis/nodejs-storage/commit/e8116d3c6fa7412858692e67745b514eef78850e))
* Point to team in correct org ([#1185](https://**************/googleapis/nodejs-storage/issues/1185)) ([0bb1909](https://**************/googleapis/nodejs-storage/commit/0bb19098013acf71cc3842f78ff333a8e356331a))
* **deps:** update dependency @google-cloud/common to v3 ([#1134](https://**************/googleapis/nodejs-storage/issues/1134)) ([774ac5c](https://**************/googleapis/nodejs-storage/commit/774ac5c75f02238418cc8ed7242297ea573ca9cb))
* **deps:** update dependency @google-cloud/paginator to v3 ([#1131](https://**************/googleapis/nodejs-storage/issues/1131)) ([c1614d9](https://**************/googleapis/nodejs-storage/commit/c1614d98e3047db379e09299b1014e80d73ed52f))
* **deps:** update dependency @google-cloud/promisify to v2 ([#1127](https://**************/googleapis/nodejs-storage/issues/1127)) ([06624a5](https://**************/googleapis/nodejs-storage/commit/06624a534cd1fdbc38455eee8d89f9f60ba75758))
* **deps:** update dependency uuid to v8 ([#1170](https://**************/googleapis/nodejs-storage/issues/1170)) ([6a98d64](https://**************/googleapis/nodejs-storage/commit/6a98d64831baf1ca1ec2f03ecc4914745cba1c86))
* **deps:** update gcs-resumable-upload, remove gitnpm usage ([#1186](https://**************/googleapis/nodejs-storage/issues/1186)) ([c78c9cd](https://**************/googleapis/nodejs-storage/commit/c78c9cde49dccb2fcd4ce10e4e9f8299d65f6838))
* **v4-policy:** encode special characters ([#1169](https://**************/googleapis/nodejs-storage/issues/1169)) ([6e48539](https://**************/googleapis/nodejs-storage/commit/6e48539d76ca27e6f4c6cf2ac0872970f7391fed))
* sync to googleapis/conformance-tests@fa559a1 ([#1167](https://**************/googleapis/nodejs-storage/issues/1167)) ([5500446](https://**************/googleapis/nodejs-storage/commit/550044619d2f17a1977c83bce5df915c6dc9578c)), closes [#1168](https://**************/googleapis/nodejs-storage/issues/1168)


### Miscellaneous Chores

* drop keepAcl parameter in file copy ([#1166](https://**************/googleapis/nodejs-storage/issues/1166)) ([5a4044a](https://**************/googleapis/nodejs-storage/commit/5a4044a8ba13f248fc4f791248f797eb0f1f3c16))


### Build System

* drop support for node.js 8.x ([b80c025](https://**************/googleapis/nodejs-storage/commit/b80c025f106052fd25554c64314b3b3520e829b5))

## [4.7.0](https://**************/googleapis/nodejs-storage/compare/v4.6.0...v4.7.0) (2020-03-26)


### Features

* add remove conditional binding ([#1107](https://**************/googleapis/nodejs-storage/issues/1107)) ([2143705](https://**************/googleapis/nodejs-storage/commit/21437053e9497aa95ef37a865ffbdbaf4134138f))
* v4 POST with signed policy ([#1102](https://**************/googleapis/nodejs-storage/issues/1102)) ([a3d5b88](https://**************/googleapis/nodejs-storage/commit/a3d5b88b8d3d25b6e16808eb5c1425aa0a8c5ecc)), closes [#1125](https://**************/googleapis/nodejs-storage/issues/1125)


### Bug Fixes

* **deps:** update dependency date-and-time to ^0.13.0 ([#1106](https://**************/googleapis/nodejs-storage/issues/1106)) ([b759605](https://**************/googleapis/nodejs-storage/commit/b7596058e130ee2d82dc2221f24220b83c04fdae))
* **deps:** update dependency gaxios to v3 ([#1129](https://**************/googleapis/nodejs-storage/issues/1129)) ([5561452](https://**************/googleapis/nodejs-storage/commit/5561452cb0b6e5a1dcabea6973db57799422abb7))
* **types:** wrap GetSignedUrlResponse ([#1119](https://**************/googleapis/nodejs-storage/issues/1119)) ([0c7ac16](https://**************/googleapis/nodejs-storage/commit/0c7ac161f808201562f60710b9ec7bce4fbf819f))

## [4.6.0](https://**************/googleapis/nodejs-storage/compare/v4.5.0...v4.6.0) (2020-03-13)


### Features

* **storage:** Add versioning as optional metadata param of createBucket ([#1090](https://**************/googleapis/nodejs-storage/issues/1090)) ([39869e3](https://**************/googleapis/nodejs-storage/commit/39869e3c6c62eabe1840f0fd884b361265e2cb76))

## [4.5.0](https://**************/googleapis/nodejs-storage/compare/v4.4.0...v4.5.0) (2020-03-06)


### Features

* **v4:** support v4 signed URL for GA ([#1072](https://**************/googleapis/nodejs-storage/issues/1072)) ([79d7b8f](https://**************/googleapis/nodejs-storage/commit/79d7b8f5a187f23e58bf84db3f4b8c4b1a921978)), closes [#1051](https://**************/googleapis/nodejs-storage/issues/1051) [#1056](https://**************/googleapis/nodejs-storage/issues/1056) [#1066](https://**************/googleapis/nodejs-storage/issues/1066) [#1068](https://**************/googleapis/nodejs-storage/issues/1068) [#1069](https://**************/googleapis/nodejs-storage/issues/1069) [#1067](https://**************/googleapis/nodejs-storage/issues/1067) [#1070](https://**************/googleapis/nodejs-storage/issues/1070)

## [4.4.0](https://**************/googleapis/nodejs-storage/compare/v4.3.2...v4.4.0) (2020-03-03)


### Features

* **bucket:** support single source in combine ([#1076](https://**************/googleapis/nodejs-storage/issues/1076)) ([#1085](https://**************/googleapis/nodejs-storage/issues/1085)) ([251a617](https://**************/googleapis/nodejs-storage/commit/251a617791ca4b0e148b741d1931013150becc02))

### [4.3.2](https://**************/googleapis/nodejs-storage/compare/v4.3.1...v4.3.2) (2020-03-02)


### Bug Fixes

* **deps:** update dependency uuid to v7 ([#1081](https://**************/googleapis/nodejs-storage/issues/1081)) ([c72d57f](https://**************/googleapis/nodejs-storage/commit/c72d57f6f2982dad512d425ee3f041b43a87c278))
* **tests:** flaky getSignedPolicy tests ([#1093](https://**************/googleapis/nodejs-storage/issues/1093)) ([531050a](https://**************/googleapis/nodejs-storage/commit/531050a05e5b1eeedb25647417a8ae9df8d76f29))

### [4.3.1](https://**************/googleapis/nodejs-storage/compare/v4.3.0...v4.3.1) (2020-02-06)


### Bug Fixes

* **samples:** fix overwritten IAM conditions sample ([#1042](https://**************/googleapis/nodejs-storage/issues/1042)) ([25d839c](https://**************/googleapis/nodejs-storage/commit/25d839ccf421276d8a4c18b2be95004ca832d84d))
* skip validation for server decompressed objects ([#1063](https://**************/googleapis/nodejs-storage/issues/1063)) ([d6e3738](https://**************/googleapis/nodejs-storage/commit/d6e37382da1ed3b72771770cb9447c62c91f26a5))
* unhandled promise rejection warning in samples ([#1056](https://**************/googleapis/nodejs-storage/issues/1056)) ([e95ec19](https://**************/googleapis/nodejs-storage/commit/e95ec19756388e6fc4fc8e0d49a40c613ed006c6))

## [4.3.0](https://**************/googleapis/nodejs-storage/compare/v4.2.0...v4.3.0) (2020-01-21)


### Features

* add support for CORS as a first class citizen ([#1020](https://**************/googleapis/nodejs-storage/issues/1020)) ([9fee6d9](https://**************/googleapis/nodejs-storage/commit/9fee6d969a1311a3db18bf523d3614adef0526ce))


### Bug Fixes

* **deps:** update dependency date-and-time to ^0.12.0 ([#1032](https://**************/googleapis/nodejs-storage/issues/1032)) ([a324564](https://**************/googleapis/nodejs-storage/commit/a324564e8f29345412047b7f6296098211e0e831))
* a couple of typos from the refactorNodeSampleStorage branch ([#1038](https://**************/googleapis/nodejs-storage/issues/1038)) ([8faa6c6](https://**************/googleapis/nodejs-storage/commit/8faa6c6698b3b7ef4857d978482209bc5077e08e))
* do not modify constructor options ([#974](https://**************/googleapis/nodejs-storage/issues/974)) ([4969148](https://**************/googleapis/nodejs-storage/commit/4969148f5e114d86aa4928109f099ec15d56fda5))
* refactor getMetadata sample into its own file ([#1008](https://**************/googleapis/nodejs-storage/issues/1008)) ([6ed1af8](https://**************/googleapis/nodejs-storage/commit/6ed1af8aadd6f72cf0957d02e403f7c551166a5c))
* refactor getRetentionPolicy sample into its own file ([#993](https://**************/googleapis/nodejs-storage/issues/993)) ([47e4ad8](https://**************/googleapis/nodejs-storage/commit/47e4ad8c8a4fd16f4f1d9d6d9bfdc9e30b1ab999))
* refactor getUniformBucketLevelAccess into its own file ([#981](https://**************/googleapis/nodejs-storage/issues/981)) ([0ba69f1](https://**************/googleapis/nodejs-storage/commit/0ba69f1b3d6093701dac927fa4543d2d911ce8b0))
* refactor rotateEncryptionKey sample into its own file ([#1030](https://**************/googleapis/nodejs-storage/issues/1030)) ([afdf0fe](https://**************/googleapis/nodejs-storage/commit/afdf0febe8760c0819736961065d134e231a0afa))
* refactor uploadEncryptedFile sample into its own file ([#1028](https://**************/googleapis/nodejs-storage/issues/1028)) ([ba4520b](https://**************/googleapis/nodejs-storage/commit/ba4520b5f925968717222ffe5d2b1dbcfbea4334))
* refactoring disableUniformBucketLevelAccess into its own file ([#980](https://**************/googleapis/nodejs-storage/issues/980)) ([1481e20](https://**************/googleapis/nodejs-storage/commit/1481e20d8332ee2806116166fb16028506487d2d))

## [4.2.0](https://**************/googleapis/nodejs-storage/compare/v4.1.3...v4.2.0) (2020-01-02)


### Features

* **iam:** getIamPolicy to support requestedPolicyVersion ([#960](https://**************/googleapis/nodejs-storage/issues/960)) ([0f38f75](https://**************/googleapis/nodejs-storage/commit/0f38f7597f5e671b4c08830f8d457f9ca2c03cd1))
* add support for Archive storage class ([#908](https://**************/googleapis/nodejs-storage/issues/908)) ([63f63f4](https://**************/googleapis/nodejs-storage/commit/63f63f448d2d220b1e77a7dcd379f7ac7fc22af3))


### Bug Fixes

* **deps:** pin TypeScript below 3.7.0 ([#952](https://**************/googleapis/nodejs-storage/issues/952)) ([1fea60c](https://**************/googleapis/nodejs-storage/commit/1fea60c04fd9762c5506c22df0992cdb8fce4ea0))
* add createBucket file and updated relevant test ([#940](https://**************/googleapis/nodejs-storage/issues/940)) ([913b43e](https://**************/googleapis/nodejs-storage/commit/913b43e66bb10bd5dbf6b0bca9e65edd48b54234))
* update PolicyDocument types ([#944](https://**************/googleapis/nodejs-storage/issues/944)) ([b1c05b2](https://**************/googleapis/nodejs-storage/commit/b1c05b27029215c8bc313a8f4f16ff13d5af2391))

### [4.1.3](https://**************/googleapis/nodejs-storage/compare/v4.1.2...v4.1.3) (2019-11-18)


### Bug Fixes

* **deps:** update dependency date-and-time to ^0.11.0 ([#934](https://**************/googleapis/nodejs-storage/issues/934)) ([c4bf1c6](https://**************/googleapis/nodejs-storage/commit/c4bf1c616d0a9402237708bddac1341c620f0542))
* **deps:** update dependency yargs to v15 ([#933](https://**************/googleapis/nodejs-storage/issues/933)) ([f40fe0c](https://**************/googleapis/nodejs-storage/commit/f40fe0c5bd4e9b89ebe007e59986580fae4d7e09))

### [4.1.2](https://**************/googleapis/nodejs-storage/compare/v4.1.1...v4.1.2) (2019-11-12)


### Bug Fixes

* do not check access of configPath ([#915](https://**************/googleapis/nodejs-storage/issues/915)) ([a21a644](https://**************/googleapis/nodejs-storage/commit/a21a6443346f91f275233a9a07fb79550035e157))
* missing snippets with jsdoc-region-tag ([#924](https://**************/googleapis/nodejs-storage/issues/924)) ([310ba90](https://**************/googleapis/nodejs-storage/commit/310ba90a48c6f02a31c1254037dfcdb4da4b7150))
* **docs:** add jsdoc-region-tag plugin ([#929](https://**************/googleapis/nodejs-storage/issues/929)) ([74526e7](https://**************/googleapis/nodejs-storage/commit/74526e7f42cfa92c18ff332d0b9e10ea3b1324cf))

### [4.1.1](https://**************/googleapis/nodejs-storage/compare/v4.1.0...v4.1.1) (2019-11-07)


### Bug Fixes

* update format for upload sample ([#918](https://**************/googleapis/nodejs-storage/issues/918)) ([d77208b](https://**************/googleapis/nodejs-storage/commit/d77208bee82dc2d76c72fbe8d5db1bfeb8e392ff))

## [4.1.0](https://**************/googleapis/nodejs-storage/compare/v4.0.1...v4.1.0) (2019-10-31)


### Features

* [Storage] Support UniformBucketLevelAccess ([#903](https://**************/googleapis/nodejs-storage/issues/903)) ([35b1bd9](https://**************/googleapis/nodejs-storage/commit/35b1bd9f6db351ad1e51b135a3f25cc5e1566600))

### [4.0.1](https://**************/googleapis/nodejs-storage/compare/v4.0.0...v4.0.1) (2019-10-31)


### Bug Fixes

* **docs:** missing quote in signed url jsdoc ([#896](https://**************/googleapis/nodejs-storage/issues/896)) ([f2d567f](https://**************/googleapis/nodejs-storage/commit/f2d567f279fee0f48c2d6a607f4066c9da372549))
* use storage.googleapis.com for api endpoint ([862fb16](https://**************/googleapis/nodejs-storage/commit/862fb16db2990c958c0097252a44c775431a7b3f))
* **signed-url:** replace encodeURIComponent with custom encoding function ([#905](https://**************/googleapis/nodejs-storage/issues/905)) ([ba41517](https://**************/googleapis/nodejs-storage/commit/ba415179f1d5951742c1b239e0500bbd2a815ddd))

## [4.0.0](https://**************/googleapis/nodejs-storage/compare/v3.5.0...v4.0.0) (2019-10-17)


### ⚠ BREAKING CHANGES

* allow leading slashes in file name (#820)

### Bug Fixes

* **deps:** update hash-stream-validation ([#884](https://**************/googleapis/nodejs-storage/issues/884)) ([96a7fc2](https://**************/googleapis/nodejs-storage/commit/96a7fc297a563819b09727990eb9ee15a421310b))
* allow leading slashes in file name ([#820](https://**************/googleapis/nodejs-storage/issues/820)) ([92e115d](https://**************/googleapis/nodejs-storage/commit/92e115dca81604909fc34e983abcf47409d3f417))

## [3.5.0](https://**************/googleapis/nodejs-storage/compare/v3.4.0...v3.5.0) (2019-10-14)


### Features

* **bucket:** add enableLogging method ([#876](https://**************/googleapis/nodejs-storage/issues/876)) ([b09ecac](https://**************/googleapis/nodejs-storage/commit/b09ecac79b70bf99e19f0f23ffcecd17e34516bb))

## [3.4.0](https://**************/googleapis/nodejs-storage/compare/v3.3.1...v3.4.0) (2019-10-10)


### Bug Fixes

* file#move do not delete origin file if same as destination ([#874](https://**************/googleapis/nodejs-storage/issues/874)) ([dcaba8a](https://**************/googleapis/nodejs-storage/commit/dcaba8a))
* pass predefined acl as destinationPredefinedAcl to qs ([#872](https://**************/googleapis/nodejs-storage/issues/872)) ([09b8fa4](https://**************/googleapis/nodejs-storage/commit/09b8fa4))


### Features

* add flag to allow disabling auto decompression by client ([#850](https://**************/googleapis/nodejs-storage/issues/850)) ([9ebface](https://**************/googleapis/nodejs-storage/commit/9ebface))
* allow setting standard Bucket storage class ([#873](https://**************/googleapis/nodejs-storage/issues/873)) ([12a99e9](https://**************/googleapis/nodejs-storage/commit/12a99e9))

### [3.3.1](https://**************/googleapis/nodejs-storage/compare/v3.3.0...v3.3.1) (2019-09-30)


### Bug Fixes

* create correct v4 signed url with cname ([#868](https://**************/googleapis/nodejs-storage/issues/868)) ([ace3b5e](https://**************/googleapis/nodejs-storage/commit/ace3b5e))

## [3.3.0](https://**************/googleapis/nodejs-storage/compare/v3.2.1...v3.3.0) (2019-09-19)


### Bug Fixes

* **deps:** update dependency @google-cloud/pubsub to ^0.32.0 ([#849](https://**************/googleapis/nodejs-storage/issues/849)) ([fdf70bb](https://**************/googleapis/nodejs-storage/commit/fdf70bb))
* add warning for unsupported keepAcl param in file#copy ([#841](https://**************/googleapis/nodejs-storage/issues/841)) ([473bda0](https://**************/googleapis/nodejs-storage/commit/473bda0))
* remove unsupported keepAcl param ([#837](https://**************/googleapis/nodejs-storage/issues/837)) ([5f69a3d](https://**************/googleapis/nodejs-storage/commit/5f69a3d))
* use storage.googleapis.com for api endpoint ([#854](https://**************/googleapis/nodejs-storage/issues/854)) ([27fa02f](https://**************/googleapis/nodejs-storage/commit/27fa02f))
* **deps:** update dependency @google-cloud/pubsub to v1 ([#858](https://**************/googleapis/nodejs-storage/issues/858)) ([31466f4](https://**************/googleapis/nodejs-storage/commit/31466f4))
* **deps:** update dependency date-and-time to ^0.10.0 ([#857](https://**************/googleapis/nodejs-storage/issues/857)) ([e9ec9cf](https://**************/googleapis/nodejs-storage/commit/e9ec9cf))


### Features

* adds support for  asyncIterators (via readable-stream@3 dependency) ([dd5ae7f](https://**************/googleapis/nodejs-storage/commit/dd5ae7f))
* allow removal of resumable upload cache ([#773](https://**************/googleapis/nodejs-storage/issues/773)) ([da943db](https://**************/googleapis/nodejs-storage/commit/da943db)), closes [#217](https://**************/googleapis/nodejs-storage/issues/217)

### [3.2.1](https://**************/googleapis/nodejs-storage/compare/v3.2.0...v3.2.1) (2019-08-28)


### Bug Fixes

* **docs:** stop redirecting reference docs to anchor, add new sample to README ([bbb5537](https://**************/googleapis/nodejs-storage/commit/bbb5537))
* **samples:** fix failing sample view IAM member-role groups ([1c4f21f](https://**************/googleapis/nodejs-storage/commit/1c4f21f))

## [3.2.0](https://**************/googleapis/nodejs-storage/compare/v3.1.0...v3.2.0) (2019-08-22)


### Bug Fixes

* **deps:** update @google-cloud/common with fixes for http ([#809](https://**************/googleapis/nodejs-storage/issues/809)) ([8598631](https://**************/googleapis/nodejs-storage/commit/8598631))
* **deps:** update dependency @google-cloud/pubsub to ^0.31.0 ([#814](https://**************/googleapis/nodejs-storage/issues/814)) ([604e564](https://**************/googleapis/nodejs-storage/commit/604e564))
* **deps:** update dependency date-and-time to ^0.9.0 ([#805](https://**************/googleapis/nodejs-storage/issues/805)) ([8739a7d](https://**************/googleapis/nodejs-storage/commit/8739a7d))
* **ts:** fix nock @~11.0.0 ([#819](https://**************/googleapis/nodejs-storage/issues/819)) ([48f9b44](https://**************/googleapis/nodejs-storage/commit/48f9b44))


### Features

* hmac service account ([#751](https://**************/googleapis/nodejs-storage/issues/751)) ([ed1ec7b](https://**************/googleapis/nodejs-storage/commit/ed1ec7b))

## [3.1.0](https://**************/googleapis/nodejs-storage/compare/v3.0.4...v3.1.0) (2019-08-09)


### Bug Fixes

* **deps:** update dependency @google-cloud/paginator to v2 ([#781](https://**************/googleapis/nodejs-storage/issues/781)) ([23244e9](https://**************/googleapis/nodejs-storage/commit/23244e9))
* **deps:** update dependency @google-cloud/pubsub to ^0.30.0 ([#778](https://**************/googleapis/nodejs-storage/issues/778)) ([7256650](https://**************/googleapis/nodejs-storage/commit/7256650))
* allow calls with no request, add JSON proto ([30fff15](https://**************/googleapis/nodejs-storage/commit/30fff15))
* **deps:** update dependency date-and-time to ^0.8.0 ([#779](https://**************/googleapis/nodejs-storage/issues/779)) ([ab2734d](https://**************/googleapis/nodejs-storage/commit/ab2734d))
* **deps:** upgrade @google-cloud/common version to show original… ([#795](https://**************/googleapis/nodejs-storage/issues/795)) ([ea63cbe](https://**************/googleapis/nodejs-storage/commit/ea63cbe))
* **deps:** use the latest extend ([#800](https://**************/googleapis/nodejs-storage/issues/800)) ([a7f0172](https://**************/googleapis/nodejs-storage/commit/a7f0172))


### Features

* **file:** allow setting configPath of resumable upload ([#642](https://**************/googleapis/nodejs-storage/issues/642)) ([a8ceb78](https://**************/googleapis/nodejs-storage/commit/a8ceb78))

### [3.0.4](https://**************/googleapis/nodejs-storage/compare/v3.0.3...v3.0.4) (2019-07-25)


### Bug Fixes

* **deps:** update dependency pumpify to v2 ([#740](https://**************/googleapis/nodejs-storage/issues/740)) ([71a4f59](https://**************/googleapis/nodejs-storage/commit/71a4f59))

### [3.0.3](https://**************/googleapis/nodejs-storage/compare/v3.0.2...v3.0.3) (2019-07-16)


### Bug Fixes

* **typescript:** make SetLabelOptions optional ([#766](https://**************/googleapis/nodejs-storage/issues/766)) ([4336882](https://**************/googleapis/nodejs-storage/commit/4336882))

### [3.0.2](https://**************/googleapis/nodejs-storage/compare/v3.0.1...v3.0.2) (2019-07-01)


### Bug Fixes

* **docs:** fix sample code in docs ([#759](https://**************/googleapis/nodejs-storage/issues/759)) ([f9e5fd8](https://**************/googleapis/nodejs-storage/commit/f9e5fd8))
* **docs:** link to reference docs section on googleapis.dev ([#753](https://**************/googleapis/nodejs-storage/issues/753)) ([5e3a96b](https://**************/googleapis/nodejs-storage/commit/5e3a96b))

### [3.0.1](https://**************/googleapis/nodejs-storage/compare/v3.0.0...v3.0.1) (2019-06-14)


### Bug Fixes

* async should be dependency ([#743](https://**************/googleapis/nodejs-storage/issues/743)) ([e542b8b](https://**************/googleapis/nodejs-storage/commit/e542b8b))

## [3.0.0](https://**************/googleapis/nodejs-storage/compare/v2.5.0...v3.0.0) (2019-06-14)


### ⚠ BREAKING CHANGES

* upgrade engines field to >=8.10.0 (#688)

### Bug Fixes

* **deps:** update dependency @google-cloud/common to v1 ([#705](https://**************/googleapis/nodejs-storage/issues/705)) ([72a9f51](https://**************/googleapis/nodejs-storage/commit/72a9f51))
* **deps:** update dependency @google-cloud/paginator to v1 ([#695](https://**************/googleapis/nodejs-storage/issues/695)) ([ada995e](https://**************/googleapis/nodejs-storage/commit/ada995e))
* **deps:** update dependency @google-cloud/promisify to v1 ([#693](https://**************/googleapis/nodejs-storage/issues/693)) ([5df2f83](https://**************/googleapis/nodejs-storage/commit/5df2f83))
* **deps:** update dependency @google-cloud/pubsub to ^0.29.0 ([#714](https://**************/googleapis/nodejs-storage/issues/714)) ([3ee1a2c](https://**************/googleapis/nodejs-storage/commit/3ee1a2c))
* **deps:** update dependency arrify to v2 ([#667](https://**************/googleapis/nodejs-storage/issues/667)) ([ce02c27](https://**************/googleapis/nodejs-storage/commit/ce02c27))
* validate action of getSignedUrl() function ([#684](https://**************/googleapis/nodejs-storage/issues/684)) ([1b09d24](https://**************/googleapis/nodejs-storage/commit/1b09d24))
* **deps:** update dependency date-and-time to ^0.7.0 ([#736](https://**************/googleapis/nodejs-storage/issues/736)) ([7071f26](https://**************/googleapis/nodejs-storage/commit/7071f26))
* **deps:** update dependency xdg-basedir to v4 ([#681](https://**************/googleapis/nodejs-storage/issues/681)) ([8b40e6a](https://**************/googleapis/nodejs-storage/commit/8b40e6a))
* **docs:** move to new client docs URL ([#738](https://**************/googleapis/nodejs-storage/issues/738)) ([a637f99](https://**************/googleapis/nodejs-storage/commit/a637f99))
* **ts:** improve return types for response metadata ([#666](https://**************/googleapis/nodejs-storage/issues/666)) ([da42bed](https://**************/googleapis/nodejs-storage/commit/da42bed))
* **types:** fix signatures of listing methods ([#703](https://**************/googleapis/nodejs-storage/issues/703)) ([42937a8](https://**************/googleapis/nodejs-storage/commit/42937a8))


### Build System

* upgrade engines field to >=8.10.0 ([#688](https://**************/googleapis/nodejs-storage/issues/688)) ([6a1fa0f](https://**************/googleapis/nodejs-storage/commit/6a1fa0f))


### Features

* add file.isPublic() function ([#708](https://**************/googleapis/nodejs-storage/issues/708)) ([f86cadb](https://**************/googleapis/nodejs-storage/commit/f86cadb))
* support apiEndpoint override ([#728](https://**************/googleapis/nodejs-storage/issues/728)) ([61eeb64](https://**************/googleapis/nodejs-storage/commit/61eeb64))

## v2.5.0

04-04-2019 12:27 PDT

This release brings an option to file#getSignedURL to create a version 4 Signed URL.

```javascript
file.getSignedUrl({
  version: 'v4', // optional, defaults to v2 (existing version)
  action: 'read',
  expires: FUTURE_DATE,
})
```

### New Features
- feat: introduce v4 signed url ([#637](https://github.com/googleapis/nodejs-storage/pull/637))

### Dependencies
- chore(deps): update dependency @types/node to v11.13.0 ([#662](https://github.com/googleapis/nodejs-storage/pull/662))
- chore(deps): update dependency @types/tmp to v0.1.0
- chore(deps): upgrade to newest version of @google-cloud/common ([#657](https://github.com/googleapis/nodejs-storage/pull/657))
- chore(deps): update dependency typescript to ~3.4.0
- chore(deps): update dependency tmp to ^0.1.0 ([#641](https://github.com/googleapis/nodejs-storage/pull/641))

### Documentation
- docs: regenerate the samples/README.md ([#649](https://github.com/googleapis/nodejs-storage/pull/649))
- docs: slight difference in how nightly synthtool run generated README ([#650](https://github.com/googleapis/nodejs-storage/pull/650))
- docs: new synthtool generated README ([#645](https://github.com/googleapis/nodejs-storage/pull/645))
- docs(samples): refactor the quickstart to match the new rubric ([#647](https://github.com/googleapis/nodejs-storage/pull/647))
- docs: update README format
- docs: add requires_billing, retire .cloud-repo-tools.json ([#644](https://github.com/googleapis/nodejs-storage/pull/644))
- docs: add additional api_id field ([#640](https://github.com/googleapis/nodejs-storage/pull/640))
- docs: document destination option ([#633](https://github.com/googleapis/nodejs-storage/pull/633))
- docs: clarify in docs, the meaning of ASIA and coldline ([#632](https://github.com/googleapis/nodejs-storage/pull/632))
- docs: add a .repo-metadata.json ([#639](https://github.com/googleapis/nodejs-storage/pull/639))

### Internal / Testing Changes
- test(v2-sign): add multi-valued headers system-test ([#646](https://github.com/googleapis/nodejs-storage/pull/646))
- refactor: replace once with onetime ([#660](https://github.com/googleapis/nodejs-storage/pull/660))
- fix: do not download cached files ([#643](https://github.com/googleapis/nodejs-storage/pull/643))
- chore: publish to npm using wombat ([#634](https://github.com/googleapis/nodejs-storage/pull/634))
- build: use per-repo npm publish token ([#630](https://github.com/googleapis/nodejs-storage/pull/630))

## v2.4.3

03-13-2019 17:10 PDT

### Bug Fixes / Implementation Changes
- fix: getSigned(Policy|Url) throws if expiration is invalid Date ([#614](https://github.com/googleapis/nodejs-storage/pull/614))
- fix: handle errors from file#createReadStream ([#615](https://github.com/googleapis/nodejs-storage/pull/615))

### Dependencies
- fix(deps): update dependency @google-cloud/paginator to ^0.2.0
- fix(deps): update dependency gcs-resumable-upload to v1 ([#619](https://github.com/googleapis/nodejs-storage/pull/619))
- fix(deps): update dependency @google-cloud/pubsub to ^0.27.0 ([#620](https://github.com/googleapis/nodejs-storage/pull/620))
- fix(deps): update dependency @google-cloud/pubsub to ^0.26.0 ([#618](https://github.com/googleapis/nodejs-storage/pull/618))
- fix(deps): update dependency @google-cloud/pubsub to ^0.25.0 ([#616](https://github.com/googleapis/nodejs-storage/pull/616))
- chore(deps): update dependency mocha to v6 ([#611](https://github.com/googleapis/nodejs-storage/pull/611))
- fix(deps): update dependency @google-cloud/promisify to ^0.4.0 ([#609](https://github.com/googleapis/nodejs-storage/pull/609))
- chore(deps): update dependency @types/tmp to v0.0.34 ([#608](https://github.com/googleapis/nodejs-storage/pull/608))
- fix(deps): update dependency yargs to v13 ([#606](https://github.com/googleapis/nodejs-storage/pull/606))

### Documentation
- docs: update links in contrib guide ([#610](https://github.com/googleapis/nodejs-storage/pull/610))
- docs: update contributing path in README ([#603](https://github.com/googleapis/nodejs-storage/pull/603))
- chore: move CONTRIBUTING.md to root ([#601](https://github.com/googleapis/nodejs-storage/pull/601))

### Internal / Testing Changes
- build: Add docuploader credentials to node publish jobs ([#624](https://github.com/googleapis/nodejs-storage/pull/624))
- build: use node10 to run samples-test, system-test etc ([#623](https://github.com/googleapis/nodejs-storage/pull/623))
- build: update release configuration
- build: use linkinator for docs test ([#607](https://github.com/googleapis/nodejs-storage/pull/607))
- build: create docs test npm scripts ([#605](https://github.com/googleapis/nodejs-storage/pull/605))
- build: test using @grpc/grpc-js in CI ([#604](https://github.com/googleapis/nodejs-storage/pull/604))
- chore: remove console.log in system test ([#599](https://github.com/googleapis/nodejs-storage/pull/599))

## v2.4.2

02-05-2019 16:55 PST
  
### Dependencies

- deps: update @google-cloud/common ([#596](https://github.com/googleapis/nodejs-storage/pull/596))
- chore(deps): update dependency typescript to ~3.3.0 ([#591](https://github.com/googleapis/nodejs-storage/pull/591))

### Documentation

- docs: add lint/fix example to contributing guide ([#594](https://github.com/googleapis/nodejs-storage/pull/594))

### Internal / Testing Changes

- test: skip public bucket system tests running under VPCSC ([#595](https://github.com/googleapis/nodejs-storage/pull/595))

## v2.4.1

01-29-2019 13:05 PST

### Implementation Changes
- fix(ts): fix Storage.createBucket overloaded signature ([#589](https://github.com/googleapis/nodejs-storage/pull/589))

### Dependencies
- fix(deps): update dependency @google-cloud/pubsub to ^0.24.0 ([#588](https://github.com/googleapis/nodejs-storage/pull/588))

## v2.4.0

01-28-2019 12:13 PST

### New Features
- fix: `expires` can be a Date, string, or number ([#548](https://github.com/googleapis/nodejs-storage/pull/548))

### Dependencies
- deps: upgrade nodejs-common ([#582](https://github.com/googleapis/nodejs-storage/pull/582))
- chore(deps): update dependency eslint-config-prettier to v4 ([#586](https://github.com/googleapis/nodejs-storage/pull/586))
- fix(deps): update dependency @google-cloud/pubsub to ^0.23.0 ([#583](https://github.com/googleapis/nodejs-storage/pull/583))
- fix(deps): update dependency concat-stream to v2 ([#563](https://github.com/googleapis/nodejs-storage/pull/563))

### Documentation
- docs(samples): Bucket Policy Only Samples ([#557](https://github.com/googleapis/nodejs-storage/pull/557))
- fix(docs): move jsdoc away from interface ([#565](https://github.com/googleapis/nodejs-storage/pull/565))

### Internal / Testing Changes
- test: Bucket Policy Only related system test ([#579](https://github.com/googleapis/nodejs-storage/pull/579))
- build: check broken links in generated docs ([#567](https://github.com/googleapis/nodejs-storage/pull/567))
- build: include only build/src in compiled source ([#572](https://github.com/googleapis/nodejs-storage/pull/572))

## v2.3.4

12-19-2018 14:21 PST

### Implementation Changes
- fix(types): file.getMetadata should resolves to Metadata, not File ([#560](https://github.com/googleapis/nodejs-storage/pull/560))

### Internal / Testing Changes
- refactor: modernize the sample tests ([#558](https://github.com/googleapis/nodejs-storage/pull/558))
- chore(build): inject yoshi automation key ([#555](https://github.com/googleapis/nodejs-storage/pull/555))
- chore: update nyc and eslint configs ([#554](https://github.com/googleapis/nodejs-storage/pull/554))
- chore: fix publish.sh permission +x ([#552](https://github.com/googleapis/nodejs-storage/pull/552))
- fix(build): fix Kokoro release script ([#551](https://github.com/googleapis/nodejs-storage/pull/551))
- build: add Kokoro configs for autorelease ([#550](https://github.com/googleapis/nodejs-storage/pull/550))

## v2.3.3

12-06-2018 17:09 PST

### Dependencies
- chore(deps): update dependency @types/configstore to v4 ([#537](https://github.com/googleapis/nodejs-storage/pull/537))
- chore(deps): update dependency @google-cloud/pubsub to ^0.22.0 ([#535](https://github.com/googleapis/nodejs-storage/pull/535))

### Documentation
- fix(docs): place doc comment above the last overload ([#544](https://github.com/googleapis/nodejs-storage/pull/544))
- docs: update readme badges ([#536](https://github.com/googleapis/nodejs-storage/pull/536))

### Internal / Testing Changes
- chore: always nyc report before calling codecov ([#543](https://github.com/googleapis/nodejs-storage/pull/543))
- chore: nyc ignore build/test by default ([#542](https://github.com/googleapis/nodejs-storage/pull/542))
- chore: update license file ([#539](https://github.com/googleapis/nodejs-storage/pull/539))

## v2.3.2

This patch release fixed an issue affecting reading from a file on GCS ([#528](https://github.com/googleapis/nodejs-storage/issues/528)).

### Dependencies
- fix(dep): upgrade teeny-request to v3.11.3 ([#529](https://github.com/googleapis/nodejs-storage/pull/529))
- fix(deps): update dependency @google-cloud/common to ^0.27.0 ([#525](https://github.com/googleapis/nodejs-storage/pull/525))

### Internal / Testing Changes
- refactor: convert sample tests from ava to mocha ([#523](https://github.com/googleapis/nodejs-storage/pull/523))
- docs(samples): updated samples code to use async await ([#521](https://github.com/googleapis/nodejs-storage/pull/521))
- chore: add synth.metadata
- fix(ts): Update bucket options types ([#518](https://github.com/googleapis/nodejs-storage/pull/518))

## v2.3.1

11-14-2018 22:15 PST

### Bug fixes
- fix: fix TypeScript and system tests ([#515](https://github.com/googleapis/nodejs-storage/pull/515))
- fix(deps): update dependency through2 to v3 ([#507](https://github.com/googleapis/nodejs-storage/pull/507))
- docs: File#setMetadata in parallel results in unpredictable state ([#504](https://github.com/googleapis/nodejs-storage/pull/504))

### Internal / Testing Changes
- chore(deps): update dependency gts to ^0.9.0 ([#514](https://github.com/googleapis/nodejs-storage/pull/514))
- chore: update eslintignore config ([#513](https://github.com/googleapis/nodejs-storage/pull/513))
- chore(deps): update dependency @google-cloud/nodejs-repo-tools to v3 ([#512](https://github.com/googleapis/nodejs-storage/pull/512))
- refactor: use object.assign where possible ([#510](https://github.com/googleapis/nodejs-storage/pull/510))
- chore: drop contributors from multiple places ([#511](https://github.com/googleapis/nodejs-storage/pull/511))
- chore: use latest npm on Windows ([#509](https://github.com/googleapis/nodejs-storage/pull/509))

## v2.3.0

### Implementation Changes
- fix(types): Fixes getSignedUrl Return Type ([#496](https://github.com/googleapis/nodejs-storage/pull/496))
-
### New Features
- Introduce Object Lifecycle Management ([#471](https://github.com/googleapis/nodejs-storage/pull/471))

### Dependencies
- chore(deps): update dependency eslint-plugin-node to v8 ([#490](https://github.com/googleapis/nodejs-storage/pull/490))

### Internal / Testing Changes
- chore: update issue templates ([#488](https://github.com/googleapis/nodejs-storage/pull/488))

## v2.2.0

### Bug Fixes
- fix: re-enable typescript types ([#484](https://github.com/googleapis/nodejs-storage/pull/484))

### Dependencies
- fix(deps): update dependency @google-cloud/common to ^0.26.0 (edited) ([#480](https://github.com/googleapis/nodejs-storage/pull/480))
- chore: Remove 'is' dependency ([#462](https://github.com/googleapis/nodejs-storage/pull/462))
- chore: upgrade teeny-request to 3.11.0 with type definitions ([#457](https://github.com/googleapis/nodejs-storage/pull/457))
- feat: use small HTTP dependency ([#416](https://github.com/googleapis/nodejs-storage/pull/416))

### Documentation
- docs: Minor docs correction ([#465](https://github.com/googleapis/nodejs-storage/pull/465))

### Internal / Testing Changes
- chore: remove old issue template ([#485](https://github.com/googleapis/nodejs-storage/pull/485))
- chore(typescript): enable noImplicitAny ([#483](https://github.com/googleapis/nodejs-storage/pull/483))
- chore(typescript): improve typescript types and update tests ([#482](https://github.com/googleapis/nodejs-storage/pull/482))
- build: run tests on node11 ([#481](https://github.com/googleapis/nodejs-storage/pull/481))
- chores(build): do not collect sponge.xml from windows builds ([#478](https://github.com/googleapis/nodejs-storage/pull/478))
- chores(build): run codecov on continuous builds ([#476](https://github.com/googleapis/nodejs-storage/pull/476))
- chore: update new issue template ([#475](https://github.com/googleapis/nodejs-storage/pull/475))
- fix: enable noImplicitAny for src/bucket.ts ([#472](https://github.com/googleapis/nodejs-storage/pull/472))
- fix(tests): use unique prefix for system tests to avoid collision with another run ([#468](https://github.com/googleapis/nodejs-storage/pull/468))
- fix: improve the types ([#467](https://github.com/googleapis/nodejs-storage/pull/467))
- chore: move class Storage to storage.ts, create index.ts that contains all exports ([#464](https://github.com/googleapis/nodejs-storage/pull/464))
- chore: add types to many unit tests ([#463](https://github.com/googleapis/nodejs-storage/pull/463))
- fix: Annotate Iam types ([#461](https://github.com/googleapis/nodejs-storage/pull/461))
- fix: complete bucket.ts noImplicitAny ([#460](https://github.com/googleapis/nodejs-storage/pull/460))
- fix: improve the types on acl.ts ([#459](https://github.com/googleapis/nodejs-storage/pull/459))
- fix: improve types (7) ([#458](https://github.com/googleapis/nodejs-storage/pull/458))
- fix: improve the types ([#453](https://github.com/googleapis/nodejs-storage/pull/453))
- chore: update build config ([#455](https://github.com/googleapis/nodejs-storage/pull/455))
- fix: improve typescript types in src/file.ts ([#450](https://github.com/googleapis/nodejs-storage/pull/450))
- build: fix codecov uploading on Kokoro ([#451](https://github.com/googleapis/nodejs-storage/pull/451))
- test: Attempt to re-enable iam#testPermissions ([#429](https://github.com/googleapis/nodejs-storage/pull/429))
- chore(deps): update dependency sinon to v7 ([#449](https://github.com/googleapis/nodejs-storage/pull/449))
- Re-generate library using /synth.py ([#448](https://github.com/googleapis/nodejs-storage/pull/448))
- Correct parameter name. ([#446](https://github.com/googleapis/nodejs-storage/pull/446))

## v2.1.0

This release brings support for Bucket/Object lock operations, as well as disable TypeScript as we continue to annotate the project with types.

### New Features
- feat: Support Bucket/Object lock operations ([#374](https://github.com/googleapis/nodejs-storage/pull/374))

### Implementation Changes
- disable types for now ([#392](https://github.com/googleapis/nodejs-storage/pull/392))
- Don't publish sourcemaps ([#412](https://github.com/googleapis/nodejs-storage/pull/412))
#### TypeScript support (in progress)
- fix: add better types for file.ts ([#436](https://github.com/googleapis/nodejs-storage/pull/436))
- fix: use ~ for typescript (and fix compile errors) ([#426](https://github.com/googleapis/nodejs-storage/pull/426))
- fix: Add typing for File#download() ([#409](https://github.com/googleapis/nodejs-storage/pull/409))
- chore: convert system tests to typescript ([#424](https://github.com/googleapis/nodejs-storage/pull/424))
- Improve TypeScript types (part 4) ([#402](https://github.com/googleapis/nodejs-storage/pull/402))
- ts: convert jsdoc types to typescript interfaces (1) ([#383](https://github.com/googleapis/nodejs-storage/pull/383))
- fix: TS definition ([#387](https://github.com/googleapis/nodejs-storage/pull/387))
- Annotate types [#3](https://github.com/googleapis/nodejs-storage/pull/3) ([#391](https://github.com/googleapis/nodejs-storage/pull/391))
- Annotate types (2) ([#388](https://github.com/googleapis/nodejs-storage/pull/388))

### Dependencies
- chore(deps): update dependency eslint-plugin-prettier to v3 ([#419](https://github.com/googleapis/nodejs-storage/pull/419))

### Documentation
- docs: Modify source location for templates ([#410](https://github.com/googleapis/nodejs-storage/pull/410))
- docs: Explain `Bucket#upload()` still exists ([#421](https://github.com/googleapis/nodejs-storage/pull/421))

### Internal / Testing Changes
- fix(tests): fix system tests on CircleCI ([#431](https://github.com/googleapis/nodejs-storage/pull/431))
- fix(tests): system-test compiles to ./build, fix relative path ([#428](https://github.com/googleapis/nodejs-storage/pull/428))
- Update kokoro config ([#425](https://github.com/googleapis/nodejs-storage/pull/425))
- chore(samples): convert samples to async/await ([#422](https://github.com/googleapis/nodejs-storage/pull/422))
- build: samples test by adding approprate test variables ([#423](https://github.com/googleapis/nodejs-storage/pull/423))
- build: bring in latest kokoro cfgs to run System tests on PRs ([#413](https://github.com/googleapis/nodejs-storage/pull/413))
- test: remove appveyor config ([#411](https://github.com/googleapis/nodejs-storage/pull/411))
- Enable prefer-const in the eslint config ([#404](https://github.com/googleapis/nodejs-storage/pull/404))
- fix(test): instantiate PubSub using new ([#403](https://github.com/googleapis/nodejs-storage/pull/403))
- fix: optionsOrCallback could be undefined if not given, check before assign ([#401](https://github.com/googleapis/nodejs-storage/pull/401))
- Fix the requesterPays methods ([#400](https://github.com/googleapis/nodejs-storage/pull/400))
- Enable no-var in eslint ([#398](https://github.com/googleapis/nodejs-storage/pull/398))
- samples: don't use USA formatted dates for expiry ([#396](https://github.com/googleapis/nodejs-storage/pull/396))
- fix: copy(): Use correct destination file name in URI ([#389](https://github.com/googleapis/nodejs-storage/pull/389))

## v2.0.3

### Implementation Changes
- Improve TypeScript types ([#381](https://github.com/googleapis/nodejs-storage/pull/381))
- Make some parameters optional ([#380](https://github.com/googleapis/nodejs-storage/pull/380))

## v2.0.2

### Implementation Changes
- Improve the types (#377)

## v2.0.1

**This fixes types declaration issues with projects using TypeScript.**

### Implementation Changes
- Enable noImplicitThis in the tsconfig ([#370](https://github.com/googleapis/nodejs-storage/pull/370))
- Fix the path to the d.ts ([#364](https://github.com/googleapis/nodejs-storage/pull/364))
- fix: make dependency on request explicit ([#361](https://github.com/googleapis/nodejs-storage/pull/361))
- fix: remove trailing slashes from bucket name. ([#266](https://github.com/googleapis/nodejs-storage/pull/266))

### Dependencies
- fix(deps): update dependency @google-cloud/common to ^0.24.0 ([#367](https://github.com/googleapis/nodejs-storage/pull/367))
- fix(deps): update dependency gcs-resumable-upload to ^0.13.0 ([#368](https://github.com/googleapis/nodejs-storage/pull/368))
- Remove unused dependencies ([#363](https://github.com/googleapis/nodejs-storage/pull/363))
- Remove safe-buffer ([#359](https://github.com/googleapis/nodejs-storage/pull/359))
- samples: update dependency @google-cloud/storage to v2 ([#350](https://github.com/googleapis/nodejs-storage/pull/350))

### Internal / Testing Changes
- Update CI config ([#371](https://github.com/googleapis/nodejs-storage/pull/371))
- build(kokoro): run docker as user node ([#358](https://github.com/googleapis/nodejs-storage/pull/358))
- build: fix multiline in circle.yml ([#357](https://github.com/googleapis/nodejs-storage/pull/357))
- fix executable modes on .sh's; add pre-system-test.sh hook ([#356](https://github.com/googleapis/nodejs-storage/pull/356))
- decrypt both service account keys ([#353](https://github.com/googleapis/nodejs-storage/pull/353))
- Retry npm install in CI ([#352](https://github.com/googleapis/nodejs-storage/pull/352))
- Add synth script and run it ([#351](https://github.com/googleapis/nodejs-storage/pull/351))

## v2.0.0

**This release has breaking changes**.  This release has a few notable breaking changes.  Please take care when upgrading!

### require syntax changes
The import style of this library has been changed to support [es module](https://nodejs.org/api/esm.html) syntax.  This provides both forward compatibility with es modules, and better supports the TypeScript and Babel ecosystems.  As a result, the import syntax has changed:

#### Old Code
```js
const storage = require('@google-cloud/storage')();
// or...
const Storage = require('@google-cloud/storage');
const storage = new Storage({
  // config...
});
```

#### New Code
```js
const {Storage} = require('@google-cloud/storage');
const storage = new Storage({
  // config...
});
```

### `bucket.upload` no longer accepts URLs
To better support a variety of HTTP clients, the remote fetching functionality of `bucket.upload` has been removed. It can be replaced with your favorite HTTP client.

#### Old Code
```js
bucket.upload('https://example.com/images/image.png', function(err, file, res) {
  // handle upload...
});
```

#### New Code

```js
const request = require('request');
const file = bucket.file(name);
const writeStream = file.createWriteStream();
request(url).pipe(writeStream);
```

### Breaking changes
- semver: do not support upload() from url (#337)
- fix: drop support for node.js 4.x and 9.x (#282)

### Features
- refactor(ts): merge initial TypeScript conversion (#334)
- feat: Add Storage#getServiceAccount(). (#331)
- Kms sample (#209)

### Bug fixes
- fix: gzip and Cache-Control headers in upload sample (#225)
- fix: move this.[ROLE]s initialization from Acl to AclAccessorRoleMethods (#252)
- fix: signedURL cname (#210) (#234)

### Internal / Testing Changes
- chore(deps): update dependency nyc to v13 (#341)
- fix(deps): update dependency @google-cloud/common to ^0.23.0 (#340)
- test: throw on deprecation (#319)
- chore(deps): update dependency eslint-config-prettier to v3 (#336)
- fix(deps): update dependency gcs-resumable-upload to ^0.12.0 (#317)
- Fix system tests for string comparisons (#328)
- chore: ignore package-lock.json (#326)
- chore: update renovate config (#322)
- chore: regen lock files (#318)
- chore(deps): lock file maintenance (#313)
- chore: move mocha options to mocha.opts (#311)
- chore(deps): lock file maintenance (#309)
- test: use strictEqual in tests (#306)
- chore(deps): update dependency eslint-plugin-node to v7 (#305)
- chore(deps): lock file maintenance (#303)
- chore(deps): lock file maintenance (#285)
- fix: test meant to assert err msg exists (#280)
- fix(deps): update dependency yargs to v12 (#270)
- fix(deps): update dependency uuid to v3.3.2 (#269)
- chore: update gcs-resumable-upload to 0.11.1 (#265)
- fix(deps): update dependency uuid to v3.3.0 (#262)
- chore(deps): update dependency sinon to v6 (#263)
- Configure Renovate (#250)
- refactor: drop repo-tool as an exec wrapper (#258)
- chore: update sample lockfiles (#256)
- fix: update linking for samples (#254)
- chore(package): update eslint to version 5.0.0 (#253)
- refactor(es6): Refactor constructor pattern as ES6 class (#246)
- Update @google-cloud/common to the latest version 🚀 (#226)
- system-tests: fix channel test. (#243)
- refactor: Update to the latest version of nodejs-common and gcs-resumable-upload (#202)
- Fix permission of bash script for Kokoro (#223)
- chore(package): update nyc to version 12.0.2 (#216)
- chore: fix prettier incompatibility (#211)
